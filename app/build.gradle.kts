import com.android.build.gradle.internal.api.ApkVariantOutputImpl

plugins {
    id("avito.android.application.compose")
    id("avito.android.hilt")
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.google.devtools.ksp)
    alias(libs.plugins.firebase.crashlytics)
    alias(libs.plugins.firebase.perf)
    alias(libs.plugins.kotlin.parcelize)
    id("realm-android")
    alias(libs.plugins.apollo3)
    alias(libs.plugins.google.services)
    alias(libs.plugins.baselineprofile)
}

android {
    namespace = "se.scmv.morocco"

    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "se.scmv.morocco"
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()
        versionCode = libs.versions.versionCode.get().toInt()
        versionName = libs.versions.versionName.get()

        vectorDrawables.useSupportLibrary = true
        multiDexEnabled = true
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        resourceConfigurations += listOf("fr", "ar")
    }

    buildTypes {
        getByName("release") {
            manifestPlaceholders["crashlyticsCollectionEnabled"] = true
            buildConfigField("IS_DEVELOPER_MODE", false)
            buildConfigField("String", "TIKTOK_APP_ID", "\"7491252446656806920\"")
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.findByName("release")
            // Ensure Baseline Profile is fresh for release builds
            baselineProfile.automaticGenerationDuringBuild = true
        }

        getByName("debug") {
            buildConfigField("IS_DEVELOPER_MODE", true)
            buildConfigField("String", "TIKTOK_APP_ID", "\"7491252446656806920\"")
            manifestPlaceholders["crashlyticsCollectionEnabled"] = false
        }

        applicationVariants.all {
            outputs.all {
                val apkName = when(buildType.name){
                    "debug" -> "Avito(${defaultConfig.versionName})-debug.apk"
                    "release" -> "Avito(${defaultConfig.versionName})-release.apk"
                    else -> "Avito(${defaultConfig.versionName}).apk"
                }
                if (this is ApkVariantOutputImpl) {
                    outputFileName = apkName
                }
            }
        }

        create("benchmark") {
            initWith(buildTypes.getByName("release"))
            matchingFallbacks += listOf("release")
            isDebuggable = false
            proguardFiles("benchmark-rules.pro")
        }
    }

    signingConfigs {
        register("release") {
            storeFile = file("../keystore")
            storePassword = "SCMVdr01d"
            keyAlias = "scmvappkey"
            keyPassword = "SCMVdr01d"
        }
        lint {
            abortOnError = false
        }
    }

    buildFeatures {
        dataBinding = true
        viewBinding = true
        buildConfig = true
    }

    bundle {
        language {
            // Specifies that the app bundle should not support
            // configuration APKs for language resources. These
            // resources are instead packaged with each base and
            // dynamic feature APK.
            enableSplit = false
        }
        density {
            //some Huawei phones handle it bad
            enableSplit = false
        }
    }

    compileOptions {
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        freeCompilerArgs = listOf("-Xjvm-default=all")
        jvmTarget = libs.versions.jvmTarget.get()
    }

    packaging {
        resources {
            excludes += setOf(
                "META-INF/core_release.kotlin_module",
                "META-INF/LICENSE.txt",
                "META-INF/LICENSE",
                "META-INF/NOTICE.txt",
                "META-INF/NOTICE",
                "META-INF/DEPENDENCIES",
                "**/*"
            )
        }
    }

    useLibrary("org.apache.http.legacy")

    sourceSets {
        getByName("main") {
            jniLibs.srcDirs("libs")
        }
    }
}

dependencies {
    implementation(libs.androidx.activity.compose)
    implementation(libs.androidx.paging.common.android)
    //implementation(libs.androidx.hilt.compiler)
    implementation(libs.androidx.hilt.navigation.compose)
    implementation ("androidx.paging:paging-compose:3.3.2")
    implementation ("androidx.paging:paging-runtime-ktx:3.3.2")
    // Files
    implementation(files("libs/chatkit-release.aar"))
    implementation(files("libs/HERE-sdk.aar"))
    implementation(files("libs/HERE-sdk.aar"))

    // Apollo dependencies
    apolloMetadata(project(":core:data"))
    implementation(project(":core:data"))
    implementation(project(":core:datastore"))

    implementation(project(":core:designsystem"))
    implementation(project(":core:domain"))
    implementation(project(":core:common"))
    implementation(project(":core:ui"))
    implementation(project(":core:analytics"))
    implementation(project(":core:orion"))
    implementation(project(":feature:authentication"))
    implementation(project(":feature:shop"))
    implementation(project(":feature:info"))
    implementation(project(":feature:account"))
    implementation(project(":feature:ad"))
    implementation(project(":orion-lib"))
    implementation(project(":core:adstickybanner"))


    implementation(libs.androidx.activity.compose)

    // Desugar
    coreLibraryDesugaring(libs.desugar.jdk.libs)

    // Core
    implementation(libs.androidx.core.splashscreen)
    implementation(libs.androidx.core.ktx)

    // Presentation
    implementation(libs.androidx.multidex)
    implementation(libs.androidx.legacy.support)
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.appcompat.resources)
    implementation(libs.androidx.fragment)
    implementation(libs.androidx.browser)
    implementation(libs.androidx.lifecycle.viewmodel)
    implementation(libs.androidx.lifecycle.livedata)
    implementation(libs.androidx.paging)
    implementation(libs.com.google.material)
    implementation(libs.com.google.flexbox)
    implementation(libs.com.github.kizitonwose)
    implementation(libs.com.github.image.picker)
    implementation(libs.com.github.touchImageView)
    implementation(libs.com.github.glide)
    ksp(libs.com.github.glide.compiler)
    implementation(libs.me.leolin)
    implementation(libs.com.facebook.shimmer)
    implementation(libs.androidx.navigation)

    // Firebase
    implementation(platform(libs.com.google.firebase.bom))
    implementation(libs.com.google.firebase.core)
    implementation(libs.com.google.firebase.invites)
    implementation(libs.com.google.firebase.messaging)
    implementation(libs.com.google.firebase.config)
    implementation(libs.com.google.firebase.analytics)
    implementation(libs.com.google.firebase.crashlytics)
    implementation(libs.com.google.firebase.perf)

    // Google services
    implementation(libs.com.google.android.gms.base)
    implementation(libs.com.google.android.gms.ads)
    implementation(libs.com.google.android.gms.auth)
    implementation(libs.com.google.android.gms.location)
    implementation(libs.com.google.android.gms.analytics)
    implementation(libs.com.google.android.gms.places)
    implementation(libs.com.google.android.gms.maps)
    implementation(libs.com.google.android.play.review)
    implementation(libs.com.google.android.play.update)
    implementation(libs.com.google.android.exoplayer)
    implementation(libs.com.google.ads.mediation)
    implementation(libs.com.google.android.billing)

    //Apollo
    implementation(libs.apollo3.runtime)
    implementation(libs.apollo3.normalized.cache)

    //Retrofit
    implementation(libs.com.squareup.retrofit2.retrofit)
    implementation(libs.com.squareup.retrofit2.retrofit.mock)
    implementation(libs.com.squareup.retrofit2.converter.gson)
    implementation(libs.com.squareup.retrofit2.adapter.rxjava2)

    implementation(platform(libs.com.squareup.okhttp3.okhttpBom))
    implementation(libs.com.squareup.okhttp3.okhttp)
    implementation(libs.com.squareup.okhttp3.logging.interceptor)
    implementation(libs.com.squareup.okhttp3.mockwebserver)

    // Facebook
    implementation(libs.com.facebook.sdk)
    implementation(libs.com.facebook.audience)
    implementation(libs.com.facebook.annotation)

    // Local
    implementation(libs.androidx.preference)
    implementation(libs.androidx.work)

    //Room
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    // RxJava
    implementation(libs.io.rxjava2.rxjava)
    implementation(libs.io.rxjava2.rxandroid)

    // Other
    implementation(libs.installreferrer)
    implementation(libs.appBoy)
    implementation(libs.eventBus)
    implementation(libs.tap.target.promt)
    implementation(libs.androidyoutubeplayer)
    implementation(libs.viewpropertyobjectanimator)
    implementation(libs.easypermissions)
    implementation(libs.androidChart)
    implementation(libs.compressor)
    implementation(group = "commons-io", name = "commons-io", version = "2.6")
    implementation (libs.androidx.hilt.work)

    // Test
    testImplementation(libs.junit)
    testImplementation(libs.robolectric)
    testImplementation(libs.mockk)
    testImplementation(libs.coroutines.test)
    implementation ("androidx.profileinstaller:profileinstaller:1.3.1")

    //clarity
    implementation(libs.microsoft.clarity)

    //... other dependencies
    implementation(libs.tiktok.business)
    //to listen for app life cycle
    implementation (libs.androidx.lifecycle.process)
    implementation (libs.androidx.lifecycle.common.java8)
}

baselineProfile {
    // Don't build on every iteration of a full assemble.
    // Instead enable generation directly for the release build variant.
    automaticGenerationDuringBuild = false
}

// Allow references to generated code
kapt {
    correctErrorTypes = true
}

// Run android test before app release
/* tasks.whenTaskAdded { task ->
    if (task.name == "assembleRelease")
        task.dependsOn("connectedAndroidTest")
}*/

apollo {
    service("avito") {
        packageName.set("se.scmv.morocco")
    }
}

inline fun <reified ValueT> com.android.build.api.dsl.VariantDimension.buildConfigField(
    name: String,
    value: ValueT
) {
    val resolvedValue = when (value) {
        is String -> "\"$value\"" // hate this
        else -> value
    }.toString()
    buildConfigField(ValueT::class.java.simpleName, name, resolvedValue)
}