package se.scmv.morocco.avitov2.favorites.master.presentation

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.account.presentation.bookmarks.master.AccountBookmarksPages
import se.scmv.morocco.account.presentation.bookmarks.master.AccountBookmarksRoute
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.authentication.presentation.AuthenticationActivity
import se.scmv.morocco.avitov2.adview.presentation.AdViewActivity
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.events.FavoriteAdEvent
import se.scmv.morocco.events.SwitchToTab
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import se.scmv.morocco.utils.EventBusManager
import se.scmv.morocco.utils.Keys
import se.scmv.morocco.widgets.BottomNavigationBar
import javax.inject.Inject

@AndroidEntryPoint
class MyFavoritesFragment : Fragment() {

    private var listener: OnSavedSearchClickListener? = null

    @Inject
    lateinit var accountRepository: AccountRepository

    override fun onAttach(context: Context) {
        super.onAttach(context)

        // Check if the hosting activity implements the interface
        if (context is OnSavedSearchClickListener) {
            listener = context
        } else {
            throw ClassCastException("$context must implement OnSavedSearchClickListener")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                AvitoTheme {
                    Surface {
                        Scaffold(
                            snackbarHost = { SnackBarHostForSnackBarController() }
                        ) {
                            var account by remember { mutableStateOf<Account?>(null) }
                            if (account != null) {
                                AccountBookmarksRoute(
                                    tab = AccountBookmarksPages.ADS,
                                    modifier = Modifier.consumeWindowInsets(it),
                                    navigateToListing = {
                                        EventBusManager.instance?.postSticky(
                                            SwitchToTab(BottomNavigationBar.NAVIGATION_POSITION_HOME)
                                        )
                                    },
                                    applySearch = {
                                        // TODO Replace this by something like SnackBarController
                                        listener?.onListingSearchTriggered(it.searchQuery)
                                    },
                                    notifyAdRemovedFromFavorites = { adId ->
                                        // TODO Replace this by something like SnackBarController
                                        EventBusManager.instance?.postSticky(
                                            FavoriteAdEvent(
                                                adId = adId,
                                                isFavorite = false,
                                                isSuccess = true
                                            )
                                        )
                                    },
                                    navigateToAdView = { listId ->
                                        val intent = Intent(requireContext(), MainActivity::class.java).apply {
                                            putExtra(Keys.AD_ID, listId)
                                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                                        }
                                        startActivity(intent)
                                        AdViewActivity.open(requireContext(), listId)
                                    },
                                    navigateToAuthentication = ::navigateToAuthentication,
                                    account = account!!,
                                    onSendMessage = { message ->
                                        // This will be handled by the AdViewRepository in the future
                                        // For now, we can show a success message or handle it differently
                                    }
                                )
                            }
                            LaunchedEffect(Unit) {
                                accountRepository.currentAccount.collectLatest {
                                    account = it
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun navigateToAuthentication() {
        val intent = Intent(requireContext(), AuthenticationActivity::class.java)
        requireActivity().startActivityForResult(
            intent,
            AuthenticationActivity.REQUEST_SIGN_IN_FAVORITES_STATS
        )
    }

    companion object {
        fun newInstance(): MyFavoritesFragment {
            return MyFavoritesFragment()
        }
    }
}

interface OnSavedSearchClickListener {
    fun onListingSearchTriggered(query: String)
}
