package se.scmv.morocco.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import se.scmv.morocco.orion.presentation.OrionFiltersSharedValuesManager
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val configRepository: ConfigRepository,
    private val accountRepository: AccountRepository,
    private val analyticsHelper: AnalyticsHelper,
    private val orionFiltersSharedValuesManager: OrionFiltersSharedValuesManager
) : ViewModel() {
    private val searchQueryFlow = MutableStateFlow("")

    private val _viewState = MutableStateFlow(HomeViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<HomeOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    private var lastRequestTime: Long = 0 // To store the last request timestamp

    init {
        observeBookmarkedSearches()
        observeSearchQuery()
        observeFiltersChanges()
    }

    fun onSearchChanged(query: String) {
        if (query.isBlank()) {
            clearSearch()
        } else {
            _viewState.update { it.copy(searchQuery = query) }
            searchQueryFlow.update { query }
        }
    }

    fun onSuggestionClicked(suggestion: SearchSuggestion) {
        viewModelScope.launch {
            clearSearch()
            val filters = configRepository.buildFiltersValuesFor(suggestion)
            _oneTimeEvents.emit(HomeOneTimeEvents.NotifySearchSuggestionSelected(filters))
            if (suggestion.isHistory.not()) {
                accountRepository.bookmarkSearchSuggestion(suggestion)
            }
        }
    }

    fun onDeleteSuggestionClicked(suggestion: SearchSuggestion) {
        viewModelScope.launch {
            accountRepository.unbookmarkSearchSuggestion(suggestion)
        }
    }

    fun onKeyboardSearchClicked() {
        viewModelScope.launch {
            val query = _viewState.value.searchQuery
            if (query.isNotBlank()) {
                orionFiltersSharedValuesManager.applyKeywordSearch(keyword = query)
            } else {
                orionFiltersSharedValuesManager.removeKeywordSearch()
            }
        }
    }

    private fun observeBookmarkedSearches() {
        viewModelScope.launch {
            accountRepository.getBookmarkedSearchSuggestions().collectLatest { recentSearches ->
                _viewState.update { it.copy(recentSearches = recentSearches.toPersistentList()) }
            }
        }
    }

    private fun observeSearchQuery() {
        viewModelScope.launch {
            searchQueryFlow.collectLatest { query ->
                if (query.isNotBlank()) {
                    val currentTime = System.currentTimeMillis()
                    // Check if it's been more than 1.5 second since the last request
                    val timeDifference = currentTime - lastRequestTime
                    // If 1 second has passed
                    if (timeDifference >= 1_500) {
                        lastRequestTime = currentTime  // Update last request time
                        fetchSuggestions(query)
                    } else {
                        // If less than 1 second passed, schedule a request after the remaining time
                        delay(1_500 - timeDifference)
                        fetchSuggestions(query)
                    }
                }
            }
        }
    }

    private fun observeFiltersChanges() {
        viewModelScope.launch {
            orionFiltersSharedValuesManager.filterValueSharedFlow.collectLatest { filters ->
                if (filters.dynamicFilters.any { it is OrionSingleSelectCategoryDropdownValue }) {
                    _viewState.update { it.copy(searchQuery = "") }
                }
            }
        }
    }

    fun onDrawerItemClicked(item: HomeDrawerItems) {
        val event = AnalyticsEvent(
            name = AnalyticsEvent.Types.ELEMENT_CLICKED,
            properties = mutableSetOf(
                Param(
                    key = AnalyticsEvent.ParamKeys.PAGE_NAME,
                    value = AnalyticsEvent.ParamValues.DRAWER
                ),
                Param(
                    key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                    value = item.analyticsName
                )
            ).apply {
                if (item == HomeDrawerItems.LANGUAGE) {
                    add(
                        Param(
                            key = AnalyticsEvent.ParamKeys.LANG,
                            value = LocaleManager.getInvertedCurrentLanguage()
                        )
                    )
                }
            }
        )
        analyticsHelper.logEvent(event)
    }

    private suspend fun fetchSuggestions(query: String) {
        _viewState.update { it.copy(isLoading = true) }
        val result = configRepository.getSearchSuggestions(query)
        when (result) {
            is Resource.Success -> _viewState.update {
                it.copy(searchSuggestions = result.data.toPersistentList(), isLoading = false)
            }

            is Resource.Failure -> Unit
        }
    }

    fun clearSearch() {
        _viewState.update {
            it.copy(
                searchQuery = "",
                searchSuggestions = persistentListOf(),
                isLoading = false
            )
        }
    }
}