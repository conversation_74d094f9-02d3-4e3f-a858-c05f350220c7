package se.scmv.morocco.authentication.presentation.private_account.merged

import androidx.compose.runtime.Stable
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class PrivateAccountMergedViewState(
    val phoneNumber: String = "",
    val phoneNumberError: UiText? = null,
    val password: String = "",
    val passwordError: UiText? = null,
    val loading: Boolean = false,
    val checkingAccount: Boolean = false,
    val accountExists: Boolean? = null,
    val showPasswordField: <PERSON>olean = false,
    val showForgotPassword: Boolean = false,
    val showGoogleSignIn: <PERSON>olean = false,
)

sealed interface PrivateAccountMergedOneTimeEvents {
    data class Success(
        val loginType: LoginType,
    ) : PrivateAccountMergedOneTimeEvents

    data class Otp(
        val phone: String
    ) : PrivateAccountMergedOneTimeEvents
}