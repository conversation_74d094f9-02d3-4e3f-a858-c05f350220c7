package se.scmv.morocco.authentication.presentation.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.navigation
import androidx.navigation.navArgument
import androidx.navigation.navOptions
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.authentication.presentation.common.SuccessScreen
import se.scmv.morocco.authentication.presentation.otp.OtpValidationReason
import se.scmv.morocco.authentication.presentation.otp.OtpValidationRoute
import se.scmv.morocco.authentication.presentation.private_account.merged.PrivateAccountMergedRoute
import se.scmv.morocco.authentication.presentation.private_account.signup.complete_info.PrivateAccountCompleteInfoRoute
import se.scmv.morocco.authentication.presentation.resset_password.master.ResetPasswordMasterRoute
import se.scmv.morocco.authentication.presentation.resset_password.private_account.update.PasswordResetRoute
import se.scmv.morocco.authentication.presentation.shop_account.signin.ShopAccountSignInRoute
import se.scmv.morocco.authentication.presentation.shop_account.signup.ShopAccountSignUpRoute
import se.scmv.morocco.ui.composableWithAnimation
import se.scmv.morocco.ui.findActivity

@Composable
fun AuthNavHost(
    modifier: Modifier = Modifier,
    navController: NavHostController,
    navigateBack: () -> Unit,
    navigateToHome: (LoginType) -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
) {
    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = AuthScreen.PRIVATE_ACCOUNT_AUTH.route
    ) {
        composableWithAnimation(route = AuthScreen.PRIVATE_ACCOUNT_AUTH.route) {
            PrivateAccountMergedRoute(
                navigateBack = navigateBack,
                navigateToStoreSignIn = {
                    navController.navigate(AuthScreen.SHOP_ACCOUNT_SIGN_IN.route)
                },
                navigateToResetPassword = {
                    navController.navigate(AuthScreen.RESET_PASSWORD.route)
                },
                navigateToOtpValidation = { phoneNumber ->
                    navController.navigate(
                        route = AuthScreen.PRIVATE_ACCOUNT_SIGN_UP_OTP_VALIDATION.route
                            .replace("{$ARG_PHONE_NUMBER}", phoneNumber)
                            .replace(
                                "{$ARG_OTP_VALIDATION_REASON}",
                                OtpValidationReason.SIGN_UP.name
                            ),
                    )
                },
            )
        }
        composableWithAnimation(
            route = AuthScreen.PRIVATE_ACCOUNT_SIGN_UP_OTP_VALIDATION.route,
            arguments = listOf(
                navArgument(ARG_PHONE_NUMBER) { type = NavType.StringType }
            )
        ) {
            OtpValidationRoute(
                navigateBack = { navController.popBackStack() },
                onOtpValidationSuccess = { success ->
                    val otpCode = success.otpCode
                    val destination = if (otpCode != null) {
                        AuthScreen.PRIVATE_ACCOUNT_SIGN_UP_COMPLETE_INFO.route
                            .replace("{$ARG_PHONE_NUMBER}", success.phoneNumber)
                            .replace("{$ARG_OTP_CODE}", success.otpCode)
                    } else {
                        AuthScreen.PRIVATE_ACCOUNT_SIGN_UP_COMPLETE_INFO.route
                            .replace("{$ARG_PHONE_NUMBER}", success.phoneNumber)
                            .split("?").first()
                    }
                    navController.navigate(route = destination)
                }
            )
        }
        composableWithAnimation(
            route = AuthScreen.PRIVATE_ACCOUNT_SIGN_UP_COMPLETE_INFO.route,
            arguments = listOf(
                navArgument(ARG_PHONE_NUMBER) { type = NavType.StringType },
                navArgument(ARG_OTP_CODE) {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                },
            )
        ) {
            PrivateAccountCompleteInfoRoute(
                navigateBack = { navController.popBackStack() },
                navigateToHome = {
                    navigateToHome(LoginType.PHONE)
                },
                navigateToWebViewScreen = navigateToWebViewScreen
            )
        }
        composableWithAnimation(route = AuthScreen.SHOP_ACCOUNT_SIGN_IN.route) {
            ShopAccountSignInRoute(
                navigateToStoreSignUp = {
                    navController.navigate(AuthScreen.SHOP_ACCOUNT_SIGN_UP.route)
                },
                navigateToResetPassword = {
                    navController.navigate(AuthScreen.RESET_PASSWORD.route)
                },
                navigateBack = { navController.popBackStack() },
                navigateToHome = {
                    navigateToHome(LoginType.EMAIL)
                },
                navigateToWebViewScreen = navigateToWebViewScreen
            )
        }
        composableWithAnimation(route = AuthScreen.SHOP_ACCOUNT_SIGN_UP.route) {
            ShopAccountSignUpRoute(
                navigateBack = { navController.popBackStack() },
                navigateToSuccess = {
                    navController.navigate(
                        route = AuthScreen.SHOP_ACCOUNT_SIGN_UP_SUCCESS.route,
                        navOptions = navOptions {
                            popUpTo(AuthScreen.PRIVATE_ACCOUNT_AUTH.route) {
                                inclusive = true
                            }
                        }
                    )
                },
                navigateToWebViewScreen = navigateToWebViewScreen
            )
        }
        composableWithAnimation(route = AuthScreen.SHOP_ACCOUNT_SIGN_UP_SUCCESS.route) {
            val context = LocalContext.current
            SuccessScreen(
                title = R.string.shop_account_sign_up_success_screen_title,
                subtitle = R.string.shop_account_sign_up_success_screen_subtitle,
                onClose = { context.findActivity()?.onBackPressedDispatcher?.onBackPressed() },
                onContinue = { context.findActivity()?.onBackPressedDispatcher?.onBackPressed() }
            )
        }
        composableWithAnimation(route = AuthScreen.RESET_PASSWORD.route) {
            ResetPasswordMasterRoute(
                navigateBack = { navController.popBackStack() },
                navigateToOtpValidation = { phoneNumber ->
                    val route =
                        AuthScreen.RESET_PASSWORD_PHONE_OTP_VALIDATION.route
                            .replace("{$ARG_PHONE_NUMBER}", phoneNumber)
                            .replace(
                                "{$ARG_OTP_VALIDATION_REASON}",
                                OtpValidationReason.PASSWORD_RESET.name
                            )
                    navController.navigate(route)
                },
                navigateToEmailSuccess = {
                    navController.navigate(
                        route = AuthScreen.RESET_PASSWORD_EMAIL_SUCCESS.route,
                        navOptions = navOptions {
                            popUpTo(AuthScreen.RESET_PASSWORD.route) {
                                inclusive = true
                            }
                        }
                    )
                }
            )
        }
        composableWithAnimation(route = AuthScreen.RESET_PASSWORD_PHONE_OTP_VALIDATION.route) {
            OtpValidationRoute(
                navigateBack = { navController.popBackStack() },
                onOtpValidationSuccess = { success ->
                    // Shouldn't happen, but as security.
                    val otpCode = requireNotNull(success.otpCode) {
                        "Otp code cannot be null! make sure you passed an otp from OtpValidationRoute."
                    }
                    val route = AuthScreen.RESET_PASSWORD_PHONE_UPDATE.route
                        .replace("{$ARG_OTP_CODE}", otpCode)
                    navController.navigate(route)
                }
            )
        }
        composableWithAnimation(
            route = AuthScreen.RESET_PASSWORD_PHONE_UPDATE.route,
            arguments = listOf(
                navArgument(ARG_OTP_CODE) { type = NavType.StringType }
            )
        ) {
            PasswordResetRoute(
                navigateBack = { navController.popBackStack() },
                navigateToPasswordResetSuccess = {
                    navController.navigate(
                        route = AuthScreen.RESET_PASSWORD_PHONE_SUCCESS.route,
                        navOptions = navOptions {
                            popUpTo(AuthScreen.RESET_PASSWORD.route) {
                                inclusive = true
                            }
                        }
                    )
                }
            )
        }
        composableWithAnimation(route = AuthScreen.RESET_PASSWORD_PHONE_SUCCESS.route) {
            SuccessScreen(
                title = R.string.reset_password_success_screen_title,
                onClose = { navController.popBackStack() },
                onContinue = { navController.popBackStack() }
            )
        }
        composable(AuthScreen.RESET_PASSWORD_EMAIL_SUCCESS.route) {
            SuccessScreen(
                title = R.string.reset_password_email_success_screen_title,
                subtitle = R.string.reset_password_email_success_screen_subtitle,
                onClose = { navController.popBackStack() },
                onContinue = { navController.popBackStack() }
            )
        }
    }
}

fun NavGraphBuilder.authNavGraph(
    navController: NavHostController,
    navigateToHome: (LoginType) -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
) {
    navigation(
        route = AuthScreen.NAV_GRAPH_ROUTE,
        startDestination = AuthScreen.PRIVATE_ACCOUNT_AUTH.route
    ) {
        composableWithAnimation(route = AuthScreen.PRIVATE_ACCOUNT_AUTH.route) {
            PrivateAccountMergedRoute(
                navigateBack = {
                    navController.navigateUp()
                },
                navigateToStoreSignIn = {
                    navController.navigate(AuthScreen.SHOP_ACCOUNT_SIGN_IN.route)
                },
                navigateToResetPassword = {
                    navController.navigate(AuthScreen.RESET_PASSWORD.route)
                },
                navigateToOtpValidation = { phoneNumber ->
                    navController.navigate(
                        route = AuthScreen.PRIVATE_ACCOUNT_SIGN_UP_OTP_VALIDATION.route
                            .replace("{$ARG_PHONE_NUMBER}", phoneNumber)
                            .replace(
                                "{$ARG_OTP_VALIDATION_REASON}",
                                OtpValidationReason.SIGN_UP.name
                            ),
                    )
                },
            )
        }
        composableWithAnimation(
            route = AuthScreen.PRIVATE_ACCOUNT_SIGN_UP_OTP_VALIDATION.route,
            arguments = listOf(
                navArgument(ARG_PHONE_NUMBER) { type = NavType.StringType }
            )
        ) {
            OtpValidationRoute(
                navigateBack = { navController.popBackStack() },
                onOtpValidationSuccess = { success ->
                    val otpCode = success.otpCode
                    val destination = if (otpCode != null) {
                        AuthScreen.PRIVATE_ACCOUNT_SIGN_UP_COMPLETE_INFO.route
                            .replace("{$ARG_PHONE_NUMBER}", success.phoneNumber)
                            .replace("{$ARG_OTP_CODE}", success.otpCode)
                    } else {
                        AuthScreen.PRIVATE_ACCOUNT_SIGN_UP_COMPLETE_INFO.route
                            .replace("{$ARG_PHONE_NUMBER}", success.phoneNumber)
                            .split("?").first()
                    }
                    navController.navigate(route = destination)
                }
            )
        }
        composableWithAnimation(
            route = AuthScreen.PRIVATE_ACCOUNT_SIGN_UP_COMPLETE_INFO.route,
            arguments = listOf(
                navArgument(ARG_PHONE_NUMBER) { type = NavType.StringType },
                navArgument(ARG_OTP_CODE) {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                },
            )
        ) {
            PrivateAccountCompleteInfoRoute(
                navigateBack = { navController.popBackStack() },
                navigateToHome = {
                    navigateToHome(LoginType.PHONE)
                },
                navigateToWebViewScreen = navigateToWebViewScreen
            )
        }
        composableWithAnimation(route = AuthScreen.SHOP_ACCOUNT_SIGN_IN.route) {
            ShopAccountSignInRoute(
                navigateToStoreSignUp = {
                    navController.navigate(AuthScreen.SHOP_ACCOUNT_SIGN_UP.route)
                },
                navigateToResetPassword = {
                    navController.navigate(AuthScreen.RESET_PASSWORD.route)
                },
                navigateBack = { navController.popBackStack() },
                navigateToHome = {
                    navigateToHome(LoginType.EMAIL)
                },
                navigateToWebViewScreen = navigateToWebViewScreen
            )
        }
        composableWithAnimation(route = AuthScreen.SHOP_ACCOUNT_SIGN_UP.route) {
            ShopAccountSignUpRoute(
                navigateBack = { navController.popBackStack() },
                navigateToSuccess = {
                    navController.navigate(
                        route = AuthScreen.SHOP_ACCOUNT_SIGN_UP_SUCCESS.route,
                        navOptions = navOptions {
                            popUpTo(AuthScreen.PRIVATE_ACCOUNT_AUTH.route) {
                                inclusive = true
                            }
                        }
                    )
                },
                navigateToWebViewScreen = navigateToWebViewScreen
            )
        }
        composableWithAnimation(route = AuthScreen.SHOP_ACCOUNT_SIGN_UP_SUCCESS.route) {
            val context = LocalContext.current
            SuccessScreen(
                title = R.string.shop_account_sign_up_success_screen_title,
                subtitle = R.string.shop_account_sign_up_success_screen_subtitle,
                onClose = { context.findActivity()?.onBackPressedDispatcher?.onBackPressed() },
                onContinue = { context.findActivity()?.onBackPressedDispatcher?.onBackPressed() }
            )
        }
        composableWithAnimation(route = AuthScreen.RESET_PASSWORD.route) {
            ResetPasswordMasterRoute(
                navigateBack = { navController.popBackStack() },
                navigateToOtpValidation = { phoneNumber ->
                    val route =
                        AuthScreen.RESET_PASSWORD_PHONE_OTP_VALIDATION.route
                            .replace("{$ARG_PHONE_NUMBER}", phoneNumber)
                            .replace(
                                "{$ARG_OTP_VALIDATION_REASON}",
                                OtpValidationReason.PASSWORD_RESET.name
                            )
                    navController.navigate(route)
                },
                navigateToEmailSuccess = {
                    navController.navigate(
                        route = AuthScreen.RESET_PASSWORD_EMAIL_SUCCESS.route,
                        navOptions = navOptions {
                            popUpTo(AuthScreen.RESET_PASSWORD.route) {
                                inclusive = true
                            }
                        }
                    )
                }
            )
        }
        composableWithAnimation(route = AuthScreen.RESET_PASSWORD_PHONE_OTP_VALIDATION.route) {
            OtpValidationRoute(
                navigateBack = { navController.popBackStack() },
                onOtpValidationSuccess = { success ->
                    // Shouldn't happen, but as security.
                    val otpCode = requireNotNull(success.otpCode) {
                        "Otp code cannot be null! make sure you passed an otp from OtpValidationRoute."
                    }
                    val route = AuthScreen.RESET_PASSWORD_PHONE_UPDATE.route
                        .replace("{$ARG_OTP_CODE}", otpCode)
                    navController.navigate(route)
                }
            )
        }
        composableWithAnimation(
            route = AuthScreen.RESET_PASSWORD_PHONE_UPDATE.route,
            arguments = listOf(
                navArgument(ARG_OTP_CODE) { type = NavType.StringType }
            )
        ) {
            PasswordResetRoute(
                navigateBack = { navController.popBackStack() },
                navigateToPasswordResetSuccess = {
                    navController.navigate(
                        route = AuthScreen.RESET_PASSWORD_PHONE_SUCCESS.route,
                        navOptions = navOptions {
                            popUpTo(AuthScreen.RESET_PASSWORD.route) {
                                inclusive = true
                            }
                        }
                    )
                }
            )
        }
        composableWithAnimation(route = AuthScreen.RESET_PASSWORD_PHONE_SUCCESS.route) {
            SuccessScreen(
                title = R.string.reset_password_success_screen_title,
                onClose = { navController.popBackStack() },
                onContinue = { navController.popBackStack() }
            )
        }
        composable(AuthScreen.RESET_PASSWORD_EMAIL_SUCCESS.route) {
            SuccessScreen(
                title = R.string.reset_password_email_success_screen_title,
                subtitle = R.string.reset_password_email_success_screen_subtitle,
                onClose = { navController.popBackStack() },
                onContinue = { navController.popBackStack() }
            )
        }
    }
}