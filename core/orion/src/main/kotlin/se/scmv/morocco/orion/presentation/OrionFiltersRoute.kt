package se.scmv.morocco.orion.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.InputChip
import androidx.compose.material3.InputChipDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.pulltorefresh.PullToRefreshDefaults
import androidx.compose.material3.pulltorefresh.pullToRefresh
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.BookmarkedSearchQuery
import se.scmv.morocco.domain.models.orion.OrionBaseComponentData
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue
import se.scmv.morocco.domain.models.orion.OrionToggle
import se.scmv.morocco.orion.components.OptionalItemsListener
import se.scmv.morocco.orion.components.OrionUiComponentToggle
import se.scmv.morocco.ui.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrionFiltersRoute(
    account: Account,
    clickedFilterId: String?,
    viewModel: OrionFiltersViewModel,
    navigateBack: () -> Unit,
    navigateBackAndToListing: () -> Unit,
    navigateToAuth: () -> Unit,
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    OrionFiltersScreen(
        filterId = clickedFilterId,
        viewState = viewState,
        navigateBack = navigateBack,
        onBookmarkSearchChanged = {
            if (account.isLogged().not()) {
                navigateToAuth()
            } else {
                viewModel.onBookmarkSearchChanged(isBookmarked = it)
            }
        },
        onClearClicked = viewModel::onClear,
        onFilterRemoveClicked = viewModel::removeFromSelectedFilters,
        onRefreshClicked = viewModel::onRefresh,
        onConfirmClicked = viewModel::onConfirm,
    )

    LaunchedEffect(viewModel) {
        viewModel.onetimeEvents.collectLatest {
            when (it) {
                is OrionFiltersOneTimeEvents.NavigateBackAndSwitchToListing -> navigateBackAndToListing()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
private fun OrionFiltersScreen(
    filterId: String?,
    viewState: OrionFiltersViewState,
    navigateBack: () -> Unit,
    onClearClicked: () -> Unit,
    onBookmarkSearchChanged: (Boolean) -> Unit,
    onFilterRemoveClicked: (ChipData) -> Unit,
    onRefreshClicked: () -> Unit,
    onConfirmClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    val alreadyScrolled = remember { mutableStateOf(false) }
    Column(modifier = modifier.fillMaxSize()) {
        val refreshState = rememberPullToRefreshState()
        OrionFiltersTopAppBar(
            navigateBack = navigateBack,
            isBookmarked = viewState.bookmarkedSearch != null,
            onBookmarkSearchChanged = onBookmarkSearchChanged,
            onClearClicked = onClearClicked
        )
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .pullToRefresh(
                    isRefreshing = viewState.isRefreshing,
                    state = refreshState,
                    enabled = false,
                    onRefresh = {}
                ),
            contentAlignment = Alignment.Center
        ) {
            when {
                viewState.isLoading -> CircularProgressIndicator()
                viewState.filters.isNotEmpty() -> Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    val lazyListState = rememberLazyListState()
                    SelectedFilters(
                        filters = viewState.selectedFilters,
                        onRemoveClicked = onFilterRemoveClicked
                    )
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        state = lazyListState,
                        contentPadding = PaddingValues(
                            start = MaterialTheme.dimens.big,
                            end = MaterialTheme.dimens.big,
                            bottom = MaterialTheme.dimens.big,
                        ),
                    ) {
                        items(items = viewState.filters) { component ->
                            component.Display(modifier = Modifier.fillMaxWidth())
                        }
                    }
                    AvPrimaryButton(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = MaterialTheme.dimens.default)
                            .padding(vertical = MaterialTheme.dimens.medium),
                        text = viewState.count?.let { count ->
                            stringResource(R.string.listing_filters_count, count)
                        } ?: stringResource(R.string.common_show),
                        loading = viewState.isCalculatingCount,
                        onClick = onConfirmClicked
                    )
                    LaunchedEffect(viewState.filters) {
                        if (alreadyScrolled.value.not()) {
                            val index = viewState.filters.indexOfFirst { filer ->
                                filer.baseData.id == filterId
                            }
                            lazyListState.animateScrollToItem(index.coerceAtLeast(0))
                            alreadyScrolled.value = true
                        } else {
                            lazyListState.scrollToItem(0)
                        }
                    }
                }

                !viewState.isLoading && viewState.error != null -> ScreenErrorState(
                    title = stringResource(R.string.common_oups),
                    description = stringResource(R.string.common_network_error_verify_and_try_later),
                    actionText = stringResource(R.string.common_refresh),
                    onActionClicked = onRefreshClicked
                )
            }
            PullToRefreshDefaults.Indicator(
                modifier = Modifier.align(Alignment.TopCenter),
                isRefreshing = viewState.isRefreshing,
                state = refreshState,
            )
        }
    }
}

@Composable
@OptIn(ExperimentalMaterial3Api::class)
private fun OrionFiltersTopAppBar(
    navigateBack: () -> Unit,
    isBookmarked: Boolean,
    onBookmarkSearchChanged: (Boolean) -> Unit,
    onClearClicked: () -> Unit
) {
    TopAppBar(
        navigationIcon = {
            FilledIconButton(
                onClick = navigateBack,
                colors = IconButtonDefaults.filledIconButtonColors(
                    containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
                )
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = null
                )
            }
        },
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
            ) {
                Text(
                    text = stringResource(R.string.listing_filters_save_search),
                    style = MaterialTheme.typography.bodyLarge,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Switch(
                    checked = isBookmarked,
                    onCheckedChange = onBookmarkSearchChanged
                )
            }
        },
        actions = {
            TextButton(
                modifier = Modifier.padding(end = MaterialTheme.dimens.large),
                onClick = onClearClicked,
                shape = MaterialTheme.shapes.small,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.onBackground
                )
            ) {
                Text(
                    text = stringResource(R.string.common_clear),
                    style = MaterialTheme.typography.bodyLarge
                )
            }
        },
        windowInsets = WindowInsets(0)
    )
}

@Composable
private fun SelectedFilters(
    filters: PersistentSet<ChipData>,
    onRemoveClicked: (ChipData) -> Unit
) {
    LazyRow(
        contentPadding = PaddingValues(horizontal = MaterialTheme.dimens.big),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
    ) {
        items(filters.size) {
            val chip = filters.elementAt(it)
            InputChip(
                label = { Text(text = chip.name) },
                onClick = { onRemoveClicked(chip) },
                selected = chip.selected,
                trailingIcon = {
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "Localized description",
                        Modifier.size(InputChipDefaults.AvatarSize)
                    )
                },
                colors = InputChipDefaults.inputChipColors(
                    selectedContainerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
                )
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview()
@Composable
private fun OrionFiltersBottomSheetPreview() {
    AvitoTheme {
        val state = remember {
            OrionFiltersViewState(
                filters = persistentListOf(
                    OrionUiComponentToggle(
                        uiConfig = OrionToggle(
                            baseData = OrionBaseComponentData(
                                id = "price",
                                title = "Ads with price only",
                                required = false,
                                iconUrl = "",
                                validations = listOf()
                            ),
                        ),
                        listener = object : OptionalItemsListener {},
                        initialValue = OrionKeyBooleanValue("price", true),
                    ),
                    OrionUiComponentToggle(
                        uiConfig = OrionToggle(
                            baseData = OrionBaseComponentData(
                                id = "image",
                                title = "Ads with images only",
                                required = false,
                                iconUrl = "",
                                validations = listOf()
                            ),
                        ),
                        listener = object : OptionalItemsListener {},
                        initialValue = null,
                    )
                ),
                bookmarkedSearch = BookmarkedSearchQuery(1, "query"),
                isLoading = false,
                isRefreshing = true,
                error = null
            )
        }
        OrionFiltersScreen(
            filterId = null,
            viewState = state,
            navigateBack = {},
            onBookmarkSearchChanged = {},
            onClearClicked = {},
            onFilterRemoveClicked = {},
            onRefreshClicked = {},
            onConfirmClicked = {},
            modifier = Modifier.background(MaterialTheme.colorScheme.background)
        )
    }
}