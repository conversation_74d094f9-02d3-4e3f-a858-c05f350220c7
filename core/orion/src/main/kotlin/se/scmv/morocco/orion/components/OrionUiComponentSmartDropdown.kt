package se.scmv.morocco.orion.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.fastFilter
import androidx.compose.ui.util.fastFirstOrNull
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import se.scmv.morocco.common.extensions.normalized
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionSmartDropDown
import se.scmv.morocco.domain.models.orion.OrionSmartDropDown.VasAction.REFRESH_VAS_PACKAGES_BY_BRAND
import se.scmv.morocco.domain.models.orion.OrionSmartDropDown.VasAction.REFRESH_VAS_PACKAGES_BY_CITY_AREA
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownItem
import se.scmv.morocco.orion.R
import java.util.regex.Pattern

private const val ROUTE_LIST = "list"
private const val ROUTE_DETAILS = "details"

sealed interface VasAction {
    data class RefreshVasPacksByCityArea(val cityId: String, val areaId: String?) : VasAction
    data class RefreshVasPacksByBrand(val brandId: String) : VasAction
}

class OrionUiComponentSmartDropdown(
    private val uiConfig: OrionSmartDropDown,
    private val listener: OptionalItemsListener,
    private val initialParentValue: OrionKeyStringValue? = null,
    private val initialChildValue: OrionKeyStringValue? = null,
) : OrionUiComponent(uiConfig.baseData) {

    private var selectedParentAndChild by mutableStateOf(
        if (initialParentValue != null) {
            uiConfig.items.fastFirstOrNull { it.id == initialParentValue.value }?.let { parent ->
                val child = parent.children.fastFirstOrNull { child ->
                    child.id == initialChildValue?.value
                }
                parent to child
            }
        } else null
    )

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    override fun Content(modifier: Modifier) {
        val scope = rememberCoroutineScope()
        var showCategoriesBtmSheet by remember { mutableStateOf(false) }
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            with(uiConfig.baseData) {
                Title(text = title, required = required)
            }
            DropDownButton(modifier) {
                showCategoriesBtmSheet = true
            }
        }
        if (showCategoriesBtmSheet) {
            val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
            DropdownBtmSheet(
                sheetState = sheetState,
                onDismiss = { selected ->
                    scope.launch { sheetState.hide() }.invokeOnCompletion {
                        showCategoriesBtmSheet = false
                        if (selected != null) {
                            selectedParentAndChild = selected
                            error = null
                            when(uiConfig.vasAction) {
                                REFRESH_VAS_PACKAGES_BY_CITY_AREA -> listener.executeVasAction(
                                    VasAction.RefreshVasPacksByCityArea(
                                        cityId = selected.first.id,
                                        areaId = selected.second?.id
                                    )
                                )
                                REFRESH_VAS_PACKAGES_BY_BRAND -> listener.executeVasAction(
                                    VasAction.RefreshVasPacksByBrand(brandId = selected.first.id)
                                )
                                else -> {}
                            }
                            listener.onValueChanged(uiConfig, collectValue())
                        }
                    }
                }
            )
        }
    }

    override fun validate(): Boolean {
        if (uiConfig.baseData.required) {
            val parentId = selectedParentAndChild?.first?.id
            uiConfig.baseData.validations.forEach {
                if (checkRegexRule(parentId.orEmpty(), it.regex).not()) {
                    error = UiText.Text(it.errorMessage)
                    return false
                }
            }
        }

        if (uiConfig.childBaseData.required) {
            val childId = selectedParentAndChild?.second?.id
            uiConfig.baseData.validations.forEach {
                if (checkRegexRule(childId.orEmpty(), it.regex).not()) {
                    error = UiText.Text(it.errorMessage)
                    return false
                }
            }
        }

        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        if (uiConfig.baseData.required.not() && selectedParentAndChild == null) return emptyList()

        val parent = requireNotNull(selectedParentAndChild?.first) {
            "OrionUiComponentSmartDropdown: ${uiConfig.baseData.title} is required but not selected, make sure you validate is called !"
        }

        return mutableListOf(
            OrionKeyStringValue(id = uiConfig.baseData.id, value = parent.id)
        ).apply {
            selectedParentAndChild?.second?.id?.let { childId ->
                add(OrionKeyStringValue(id = uiConfig.childBaseData.id, value = childId))
            }
        }
    }

    override fun resetValue() {
        selectedParentAndChild = null
    }

    @Composable
    private fun DropDownButton(
        modifier: Modifier,
        onClick: () -> Unit
    ) {
        Row(
            modifier = modifier
                .fillMaxWidth()
                .border(width = 1.dp, color = MaterialTheme.colorScheme.outline, shape = MaterialTheme.shapes.small)
                .clip(shape = MaterialTheme.shapes.small)
                .clickable(onClick = onClick)
                .padding(all = MaterialTheme.dimens.large),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier.width(IntrinsicSize.Max),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
            ) {
                Icon(url = uiConfig.baseData.iconUrl)
                val name = selectedParentAndChild?.let {
                    it.second?.name?.let { childName ->
                        "${it.first.name} - $childName"
                    } ?: it.first.name
                }
                Text(
                    text = name ?: stringResource(R.string.common_select),
                    style = MaterialTheme.typography.bodyMedium,
                )
            }
            androidx.compose.material3.Icon(
                modifier = Modifier.width(IntrinsicSize.Min),
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "Open smart dropdown icon"
            )
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun DropdownBtmSheet(
        sheetState: SheetState,
        onDismiss: (Pair<OrionSmartDropDownItem, OrionSmartDropDownItem?>?) -> Unit,
    ) {
        ModalBottomSheet(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding(),
            onDismissRequest = { onDismiss(null) },
            sheetState = sheetState,
        ) {
            val navController = rememberNavController()
            var selectedParent by remember { mutableStateOf<OrionSmartDropDownItem?>(null) }
            NavHost(navController = navController, startDestination = ROUTE_LIST) {
                composable(route = ROUTE_LIST) {
                    DropdownBtmSheetScreen(
                        title = uiConfig.baseData.title,
                        items = uiConfig.items.toImmutableList(),
                        onDismiss = { onDismiss(null) },
                        onItemSelected = { item ->
                            if (item.children.size <= 1) {
                                onDismiss(item to item.children.firstOrNull())
                            } else {
                                selectedParent = item
                                navController.navigate(ROUTE_DETAILS)
                            }
                        }
                    )
                }
                composable(route = ROUTE_DETAILS) {
                    DropdownBtmSheetScreen(
                        title = uiConfig.childBaseData.title,
                        items = selectedParent?.children.orEmpty().toImmutableList(),
                        onDismiss = { onDismiss(null) },
                        onItemSelected = { item ->
                            navController.popBackStack()
                            selectedParent?.let { parent -> onDismiss(parent to item) }
                        },
                        onBack = {
                            if (navController.currentDestination?.route == ROUTE_DETAILS) {
                                navController.popBackStack()
                            }
                        }
                    )
                }
            }
        }
    }

    @Composable
    private fun DropdownBtmSheetSearchView(
        modifier: Modifier = Modifier,
        text: String,
        onSearch: (String) -> Unit
    ) {
        val keyboardController = LocalSoftwareKeyboardController.current
        AvTextField(
            modifier = modifier.fillMaxWidth(),
            value = text,
            onValueChanged = onSearch,
            placeholder = R.string.common_search,
            leadingIcon = {
                androidx.compose.material3.Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = null
                )
            },
            trailingIcon = {
                if (text.isNotBlank()) {
                    IconButton(onClick = { onSearch("") }) {
                        androidx.compose.material3.Icon(
                            modifier = Modifier.size(MaterialTheme.dimens.big),
                            imageVector = Icons.Default.Clear,
                            contentDescription = null
                        )
                    }
                }
            },
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(
                onDone = {
                    keyboardController?.hide()
                }
            ),
            shape = CircleShape
        )
    }

    @OptIn(ExperimentalMaterial3Api::class, FlowPreview::class)
    @Composable
    fun DropdownBtmSheetScreen(
        title: String,
        items: ImmutableList<OrionSmartDropDownItem>,
        onDismiss: () -> Unit,
        onItemSelected: (OrionSmartDropDownItem) -> Unit,
        onBack: (() -> Unit)? = null
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            TopAppBar(
                title = { Text(text = title, style = MaterialTheme.typography.labelLarge) },
                navigationIcon = { },
                actions = {
                    IconButton(onClick = onDismiss) {
                        androidx.compose.material3.Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = null
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surfaceContainerLow
                )
            )
            HorizontalDivider()
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(horizontal = MaterialTheme.dimens.default),
            ) {
                val scope = rememberCoroutineScope()
                var dropdownItems by remember { mutableStateOf(items) }
                var searchText by remember { mutableStateOf("") }
                val searchFlow = remember { MutableStateFlow("") }
                LaunchedEffect(Unit) {
                    searchFlow
                        .debounce(300)
                        .collect { searchQuery ->
                            scope.launch(Dispatchers.Default) {
                                val filteredItems = if (searchQuery.isEmpty()) {
                                    items
                                } else {
                                    items.fastFilter {
                                        it.name.normalized().contains(
                                            searchQuery.normalized(),
                                            ignoreCase = true
                                        )
                                    }
                                }
                                // Update UI on main thread
                                withContext(Dispatchers.Main) {
                                    dropdownItems = filteredItems.toImmutableList()
                                }
                            }
                        }
                }
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
                DropdownBtmSheetSearchView(text = searchText) { text ->
                    if (text != searchText) {
                        searchText = text
                        searchFlow.value = text
                    }
                }
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
                HorizontalDivider()
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
                if (onBack != null) {
                    TextButton(onClick = onBack) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                        ) {
                            androidx.compose.material3.Icon(
                                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowLeft,
                                contentDescription = "Back icon"
                            )
                            Text(
                                text = stringResource(R.string.common_back),
                                style = MaterialTheme.typography.bodyMedium,
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
                    HorizontalDivider()
                }
                LazyColumn(modifier = Modifier.fillMaxSize()) {
                    items(dropdownItems) { item ->
                        DropdownItem(
                            name = item.name,
                            selected = selectedParentAndChild?.first == item || selectedParentAndChild?.second == item,
                            hasChildren = item.children.size > 1,
                            onClick = { onItemSelected(item) }
                        )
                    }
                }
            }
        }
    }

    @Composable
    private fun DropdownItem(
        modifier: Modifier = Modifier,
        name: String,
        selected: Boolean,
        hasChildren: Boolean,
        onClick: () -> Unit
    ) {
        Row(
            modifier = modifier
                .fillMaxWidth()
                .clip(shape = MaterialTheme.shapes.extraSmall)
                .background(
                    color = if (selected) {
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.12f)
                    } else Color.Transparent
                )
                .clickable(onClick = onClick)
                .padding(all = MaterialTheme.dimens.default),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = name,
                style = MaterialTheme.typography.bodyMedium,
                overflow = TextOverflow.Ellipsis
            )
            Row(verticalAlignment = Alignment.CenterVertically) {
                if (selected) {
                    androidx.compose.material3.Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Selected category check mark"
                    )
                }
                if (hasChildren) {
                    androidx.compose.material3.Icon(
                        imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                        contentDescription = "Show children icon"
                    )
                }
            }
        }
    }

    private fun checkRegexRule(text: String, regex: String): Boolean {
        val modifiedText = text.replace("\n", " ")
        val matcher = Pattern.compile(regex).matcher(modifiedText)
        return matcher.find()
    }
}