package se.scmv.morocco.orion.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.CachePolicy
import coil.request.ImageRequest
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.launch
import se.scmv.morocco.designsystem.components.AvChipGroup
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.components.ChipStateHandler
import se.scmv.morocco.designsystem.components.avTextFieldColors
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.AdType
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.CATEGORY_ID_ALL
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdown
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.orion.R

class OrionUiComponentSingleSelectCategoryDropdown(
    private val uiConfig: OrionSingleSelectCategoryDropdown,
    private val initialCategoryId: String,
    private val initialAdTypeKey: AdTypeKey?,
    private val categories: List<CategoryTree>,
    private val listener: OptionalItemsListener
) : OrionUiComponent(baseData = uiConfig.baseData) {

    private var adTypes: List<ChipData> by mutableStateOf(emptyList())
    private var selectedCategory: CategoryTree by mutableStateOf(
        requireNotNull(
            CategoryTree.findTree(
                trees = categories,
                categoryId = initialCategoryId,
                adTypeKey = initialAdTypeKey
            )
        ) {
            "Category $initialCategoryId not present in the list !!"
        }.also { tree ->
            adTypes = tree.adTypes.map { it.toChip(it.key == initialAdTypeKey) }
            if (adTypes.none { it.selected }) {
                adTypes = adTypes.toMutableList().apply {
                    getOrNull(0)?.copy(selected = true)?.let { set(0, it) }
                }.toList()
            }
        }
    )

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    override fun Content(modifier: Modifier) {
        val scope = rememberCoroutineScope()
        var showCategoriesBtmSheet by remember { mutableStateOf(false) }
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            with(uiConfig.baseData) {
                Title(text = title, required = required)
            }
            DropdownButton(
                icon = selectedCategory.category.icon,
                text = selectedCategory.category.name,
                enabled = uiConfig.enabled,
                onClick = { showCategoriesBtmSheet = true }
            )
            if (selectedCategory.adTypes.size > 1) {
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
                AvChipGroup(
                    chips = adTypes.toImmutableList(),
                    onChipClicked = {
                        adTypes = ChipStateHandler.handleMonoSelect(
                            currentState = adTypes,
                            clickedChip = it
                        )
                    }
                )
            }
        }
        if (showCategoriesBtmSheet) {
            val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
            CategoriesBottomSheet(
                sheetState = sheetState,
                trees = categories.filter {
                    // This is a hack to hide the "Tout" category from the dropdown. Should be handled better.
                    it.category.id != CATEGORY_ID_ALL
                }.toImmutableList(),
                onDismiss = { tree ->
                    scope.launch {
                        sheetState.hide()
                        showCategoriesBtmSheet = false
                        if (tree != null) {
                            selectedCategory = tree
                            adTypes = tree.adTypes.mapIndexed { index, adType ->
                                adType.toChip(index == 0)
                            }
                            listener.onValueChanged(uiConfig, collectValue())
                        }
                    }
                },
            )
        }
    }

    override fun validate(): Boolean {
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        return listOf(
            OrionSingleSelectCategoryDropdownValue(
                id = uiConfig.baseData.id,
                category = selectedCategory.category,
                adTypeKey = adTypes.firstOrNull { it.selected }?.id?.let { AdTypeKey.valueOf(it) }
            )
        )
    }

    override fun resetValue() {
        selectedCategory = requireNotNull(
            CategoryTree.findTree(
                trees = categories,
                categoryId = CATEGORY_ID_ALL,
                adTypeKey = AdTypeKey.ALL
            )
        ) {
            "OrionUiComponentSingleSelectCategoryDropdown resetValue: Category $CATEGORY_ID_ALL not present in the list !!"
        }
        adTypes = selectedCategory.adTypes.map { it.toChip(true) }
        listener.onValueChanged(uiConfig, collectValue())
    }

    @Composable
    private fun DropdownButton(
        icon: String,
        text: String,
        enabled: Boolean,
        onClick: () -> Unit
    ) {
        var modifier = Modifier
            .fillMaxWidth()
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.outline,
                shape = MaterialTheme.shapes.small
            )
            .clip(shape = MaterialTheme.shapes.small)
        modifier = if (enabled) {
            modifier.clickable(onClick = onClick)
        } else {
            modifier.background(avTextFieldColors.disabledContainerColor)
        }
        modifier = modifier.padding(all = MaterialTheme.dimens.default)
        Row(
            modifier = modifier,
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier.width(IntrinsicSize.Max),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
            ) {
                CategoryIcon(url = icon)
                Text(
                    text = text,
                    color = if (enabled) Color.Unspecified else Color.Black,
                    style = MaterialTheme.typography.bodyMedium,
                )
            }
            androidx.compose.material3.Icon(
                modifier = Modifier.width(IntrinsicSize.Min),
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "Open categories dropdown icon"
            )
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun CategoriesBottomSheet(
        sheetState: SheetState,
        trees: ImmutableList<CategoryTree>,
        onDismiss: (CategoryTree?) -> Unit,
    ) {
        val scope = rememberCoroutineScope()
        val pagerState = rememberPagerState { trees.size }
        var selectedCategoryToConfirm by remember { mutableStateOf(selectedCategory) }
        ModalBottomSheet(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding(),
            onDismissRequest = { onDismiss(null) },
            sheetState = sheetState,
        ) {
            HorizontalDivider()
            Row(
                modifier = Modifier.weight(1f)
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
                ) {
                    trees.forEachIndexed { index, tree ->
                        val selected = index == pagerState.currentPage
                        CategoryNode(
                            name = tree.category.name,
                            icon = tree.category.icon,
                            selected = selected,
                            onClick = {
                                if (uiConfig.allowParentSelection) {
                                    selectedCategoryToConfirm = tree
                                }
                                scope.launch { pagerState.scrollToPage(index) }
                            }
                        )
                    }
                }
                VerticalDivider()
                VerticalPager(
                    modifier = Modifier.fillMaxSize(),
                    state = pagerState,
                    userScrollEnabled = false,
                ) {
                    var expandedTree by remember { mutableStateOf<CategoryTree?>(null) }
                    CategoryTrees(
                        modifier = Modifier
                            .fillMaxSize()
                            .verticalScroll(rememberScrollState()),
                        trees = trees[it].children.toImmutableList(),
                        expandedTree = expandedTree,
                        onExpanded = { tree ->
                            if (uiConfig.allowParentSelection) {
                                selectedCategoryToConfirm = tree
                            }
                            expandedTree = if (expandedTree?.category?.id == tree.category.id) {
                                null
                            } else tree
                        },
                        selectedTree = if (uiConfig.allowParentSelection) {
                            selectedCategoryToConfirm
                        } else selectedCategory,
                        onSelectCategory = { tree ->
                            if (uiConfig.allowParentSelection) {
                                selectedCategoryToConfirm = tree
                            } else onDismiss(tree)
                        }
                    )
                }
            }
            if (uiConfig.allowParentSelection) {
                Column(
                    modifier = Modifier
                        .padding(horizontal = MaterialTheme.dimens.medium)
                        .padding(top = MaterialTheme.dimens.default)
                        .padding(bottom = MaterialTheme.dimens.large)
                        .fillMaxWidth()
                        .systemBarsPadding(),
                ) {
                    HorizontalDivider()
                    AvPrimaryButton(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(R.string.common_apply)
                    ) {
                        onDismiss(selectedCategoryToConfirm)
                    }
                }
            }
        }
        LaunchedEffect(Unit) {
            val indexOfSelected = trees.indexOfFirst {
                CategoryTree.hasCategoryWithId(tree = it, categoryId = selectedCategory.category.id)
            }
            if (indexOfSelected > 0) {
                pagerState.scrollToPage(indexOfSelected)
            }
        }
    }

    @Composable
    private fun CategoryNode(
        modifier: Modifier = Modifier,
        name: String,
        icon: String,
        selected: Boolean,
        onClick: () -> Unit
    ) {
        Box(
            modifier = modifier
                .width(80.dp)
                .aspectRatio(1f)
                .clickable(onClick = onClick),
        ) {
            VerticalDivider(
                modifier = Modifier.align(Alignment.TopStart),
                thickness = 3.dp,
                color = if (selected) {
                    MaterialTheme.colorScheme.primary
                } else Color.Unspecified
            )
            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(horizontal = MaterialTheme.dimens.small),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                CategoryIcon(
                    modifier = Modifier.size(MaterialTheme.dimens.bigger),
                    url = icon,
                )
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = name,
                    style = MaterialTheme.typography.labelMedium,
                    color = if (selected) {
                        MaterialTheme.colorScheme.primary
                    } else Color.Unspecified,
                    textAlign = TextAlign.Center
                )
            }
        }
    }

    @Composable
    fun CategoryTrees(
        modifier: Modifier = Modifier,
        trees: ImmutableList<CategoryTree>,
        expandedTree: CategoryTree?,
        onExpanded: (CategoryTree) -> Unit,
        selectedTree: CategoryTree,
        onSelectCategory: (CategoryTree) -> Unit
    ) {
        Column(modifier) {
            trees.forEach { tree ->
                CategoryTree(
                    tree = tree,
                    selectedTree = selectedTree,
                    expandedTree = expandedTree,
                    onExpanded = onExpanded,
                    onSelectCategory = onSelectCategory
                )
            }
        }
    }

    @Composable
    private fun CategoryTree(
        tree: CategoryTree,
        selectedTree: CategoryTree,
        expandedTree: CategoryTree?,
        onExpanded: (CategoryTree) -> Unit,
        onSelectCategory: (CategoryTree) -> Unit
    ) {
        val hasChildren = remember { tree.children.isNotEmpty() }
        CategoryItem(
            modifier = Modifier,
            categoryName = tree.category.name,
            categoryIcon = tree.category.icon,
            hasChildren = hasChildren,
            expanded = tree.category.id == expandedTree?.category?.id,
            selected = tree.category.id == selectedTree.category.id
                    && tree.adTypes == selectedTree.adTypes,
            onClick = {
                if (hasChildren) {
                    onExpanded(tree)
                } else {
                    onSelectCategory(tree)
                }
            }
        )
        if (hasChildren) {
            AnimatedVisibility(
                visible = tree.category.id == expandedTree?.category?.id,
                enter = fadeIn()
            ) {
                CategoryTrees(
                    modifier = Modifier.padding(start = MaterialTheme.dimens.default),
                    trees = tree.children.toImmutableList(),
                    expandedTree = expandedTree,
                    onExpanded = onExpanded,
                    selectedTree = selectedTree,
                    onSelectCategory = onSelectCategory,
                )
            }
        }
    }

    @Composable
    private fun CategoryItem(
        modifier: Modifier = Modifier,
        categoryName: String,
        categoryIcon: String,
        hasChildren: Boolean,
        expanded: Boolean,
        selected: Boolean,
        onClick: () -> Unit
    ) {
        Column {
            Row(
                modifier = modifier
                    .fillMaxWidth()
                    .clip(shape = MaterialTheme.shapes.extraSmall)
                    .background(
                        color = if (selected) {
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.12f)
                        } else Color.Transparent
                    )
                    .clickable(onClick = onClick)
                    .padding(all = MaterialTheme.dimens.default),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                ) {
                    CategoryIcon(
                        modifier = Modifier.size(MaterialTheme.dimens.bigger),
                        url = categoryIcon,
                    )
                    Text(
                        modifier = Modifier.weight(1f),
                        text = categoryName,
                        style = MaterialTheme.typography.bodyMedium,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                if (hasChildren) {
                    androidx.compose.material3.Icon(
                        modifier = Modifier.size(MaterialTheme.dimens.bigger),
                        imageVector = if (expanded) {
                            Icons.Filled.KeyboardArrowUp
                        } else Icons.Filled.KeyboardArrowDown,
                        contentDescription = "Expand category"
                    )
                }
            }
            HorizontalDivider()
        }
    }

    @Composable
    private fun CategoryIcon(
        modifier: Modifier = Modifier,
        url: String,
        colorFilter: ColorFilter? = null
    ) {
        AsyncImage(
            modifier = modifier.size(MaterialTheme.dimens.bigger),
            model = ImageRequest.Builder(context = LocalContext.current)
                .data(url)
                .decoderFactory(SvgDecoder.Factory())
                .memoryCacheKey(url)
                .memoryCachePolicy(CachePolicy.ENABLED)
                .build(),
            contentDescription = null,
            error = painterResource(R.drawable.ic_ad_insert_step1),
            colorFilter = colorFilter
        )
    }

    private fun AdType.toChip(
        selected: Boolean
    ) = ChipData(id = key.name, name = name, selected = selected)
}