package se.scmv.morocco.domain.models

import org.junit.Test
import org.junit.Assert.*
import se.scmv.morocco.analytics.models.AnalyticsEvent

class CategoryAnalyticsTest {

    @Test
    fun `getAnalyticsParams for vertical level category should only include vertical params`() {
        // Given: A root category (vertical level)
        val realEstate = Category("1010", "Real Estate", "house", "RealEstate")
        
        // When
        val params = realEstate.getAnalyticsParams()
        
        // Then
        assertEquals(2, params.size)
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.VERTICAL_ID && it.value == "1010" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.VERTICAL_NAME && it.value == "RealEstate" })
        assertFalse(params.any { it.key == AnalyticsEvent.ParamKeys.CATEGORY_ID })
        assertFalse(params.any { it.key == AnalyticsEvent.ParamKeys.SUB_CATEGORY_ID })
    }

    @Test
    fun `getAnalyticsParams for category level should include vertical and category params`() {
        // Given: A 2-level hierarchy
        val realEstate = Category("1010", "Real Estate", "house", "RealEstate")
        val apartments = Category("1011", "Apartments", "apartment", "Apartments", realEstate)
        
        // When
        val params = apartments.getAnalyticsParams()
        
        // Then
        assertEquals(4, params.size)
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.VERTICAL_ID && it.value == "1010" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.VERTICAL_NAME && it.value == "RealEstate" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.CATEGORY_ID && it.value == "1011" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.CATEGORY_NAME && it.value == "Apartments" })
        assertFalse(params.any { it.key == AnalyticsEvent.ParamKeys.SUB_CATEGORY_ID })
    }

    @Test
    fun `getAnalyticsParams for subcategory level should include all params`() {
        // Given: A 3-level hierarchy
        val realEstate = Category("1010", "Real Estate", "house", "RealEstate")
        val apartments = Category("1011", "Apartments", "apartment", "Apartments", realEstate)
        val studioApartments = Category("1012", "Studio Apartments", "studio", "StudioApartments", apartments)
        
        // When
        val params = studioApartments.getAnalyticsParams()
        
        // Then
        assertEquals(6, params.size)
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.VERTICAL_ID && it.value == "1010" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.VERTICAL_NAME && it.value == "RealEstate" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.CATEGORY_ID && it.value == "1011" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.CATEGORY_NAME && it.value == "Apartments" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.SUB_CATEGORY_ID && it.value == "1012" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.SUB_CATEGORY_NAME && it.value == "StudioApartments" })
    }

    @Test
    fun `getAnalyticsParams for deeper hierarchy should treat as subcategory with immediate parent as category`() {
        // Given: A 4-level hierarchy
        val realEstate = Category("1010", "Real Estate", "house", "RealEstate")
        val apartments = Category("1011", "Apartments", "apartment", "Apartments", realEstate)
        val studioApartments = Category("1012", "Studio Apartments", "studio", "StudioApartments", apartments)
        val luxuryStudio = Category("1013", "Luxury Studio", "luxury", "LuxuryStudio", studioApartments)
        
        // When
        val params = luxuryStudio.getAnalyticsParams()
        
        // Then
        assertEquals(6, params.size)
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.VERTICAL_ID && it.value == "1010" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.VERTICAL_NAME && it.value == "RealEstate" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.CATEGORY_ID && it.value == "1012" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.CATEGORY_NAME && it.value == "StudioApartments" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.SUB_CATEGORY_ID && it.value == "1013" })
        assertTrue(params.any { it.key == AnalyticsEvent.ParamKeys.SUB_CATEGORY_NAME && it.value == "LuxuryStudio" })
    }

    @Test
    fun `getTopParentCategory should return root category for any level`() {
        // Given: A 3-level hierarchy
        val realEstate = Category("1010", "Real Estate", "house", "RealEstate")
        val apartments = Category("1011", "Apartments", "apartment", "Apartments", realEstate)
        val studioApartments = Category("1012", "Studio Apartments", "studio", "StudioApartments", apartments)
        
        // When & Then
        assertEquals(realEstate, realEstate.getTopParentCategory())
        assertEquals(realEstate, apartments.getTopParentCategory())
        assertEquals(realEstate, studioApartments.getTopParentCategory())
    }
}
