package se.scmv.morocco.domain.models

import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param

const val ORION_CATEGORY_KEY = "category"
const val CATEGORY_ID_ALL = "0"
const val AD_INSERT_DEFAULT_CATEGORY_ID = "1010"
val AD_INSERT_DEFAULT_AD_TYPE_KEY = AdTypeKey.SELL

data class Category(
    val id: String,
    val name: String,
    val icon: String,
    val trackingName: String,
    val parent: Category? = null
) {
    fun getAnalyticsParams(): Set<Param> {
        val topParent = getTopParentCategory()
        val params = mutableSetOf(
            Param(key = AnalyticsEvent.ParamKeys.VERTICAL_ID, value = topParent.id),
            Param(key = AnalyticsEvent.ParamKeys.VERTICAL_NAME, value = topParent.trackingName)
        )

        // Determine the level of the current category in the hierarchy
        val hierarchyLevel = getHierarchyLevel()

        when (hierarchyLevel) {
            1 -> {
                // Current category is the vertical (top level) - no category or subcategory
                // Only vertical params are added (already added above)
            }
            2 -> {
                // Current category is at category level (middle level)
                params.add(Param(key = AnalyticsEvent.ParamKeys.CATEGORY_ID, value = id))
                params.add(Param(key = AnalyticsEvent.ParamKeys.CATEGORY_NAME, value = trackingName))
            }
            3 -> {
                // Current category is at subcategory level (bottom level)
                val immediateParent = parent
                if (immediateParent != null) {
                    params.add(Param(key = AnalyticsEvent.ParamKeys.CATEGORY_ID, value = immediateParent.id))
                    params.add(Param(key = AnalyticsEvent.ParamKeys.CATEGORY_NAME, value = immediateParent.trackingName))
                }
                params.add(Param(key = AnalyticsEvent.ParamKeys.SUB_CATEGORY_ID, value = id))
                params.add(Param(key = AnalyticsEvent.ParamKeys.SUB_CATEGORY_NAME, value = trackingName))
            }
            else -> {
                // For deeper hierarchies (level 4+), treat as subcategory with immediate parent as category
                val immediateParent = parent
                if (immediateParent != null) {
                    params.add(Param(key = AnalyticsEvent.ParamKeys.CATEGORY_ID, value = immediateParent.id))
                    params.add(Param(key = AnalyticsEvent.ParamKeys.CATEGORY_NAME, value = immediateParent.trackingName))
                }
                params.add(Param(key = AnalyticsEvent.ParamKeys.SUB_CATEGORY_ID, value = id))
                params.add(Param(key = AnalyticsEvent.ParamKeys.SUB_CATEGORY_NAME, value = trackingName))
            }
        }

        return params
    }

    /**
     * Returns the hierarchy level of this category (1 = vertical/root, 2 = category, 3 = subcategory, etc.)
     *
     * @return The level in the hierarchy where 1 is the root level
     */
    private fun getHierarchyLevel(): Int {
        var level = 1
        var current: Category? = this
        while (current?.parent != null) {
            level++
            current = current.parent
        }
        return level
    }

    /**
     * Returns the topmost ancestor category in the hierarchy.
     *
     * This function traverses the category hierarchy upward, starting from the current category
     * and following parent references until it reaches a category that has no parent.
     *
     * @return The root category in the hierarchy to which this category belongs.
     */
    private fun getTopParentCategory(): Category {
        var category: Category = this
        var parent: Category? = category.parent
        while (parent != null) {
            category = parent
            parent = category.parent
        }
        return category
    }

    /**
     * Returns the parent category if it exists and is not the top-level category.
     *
     * This function helps navigate the category hierarchy by providing access to the
     * immediate parent while avoiding returning the top-level category.
     *
     * @return The immediate parent category if it exists and is not the top-level category,
     * otherwise null.
     */
    private fun getParentCategoryIfNotTop(): Category? {
        val topParent = getTopParentCategory()
        return if (parent != null && parent != topParent) parent else null
    }
}

data class CategoryTree(
    val category: Category,
    val adTypes: List<AdType>,
    val children: List<CategoryTree>,
) {
    companion object {
        fun findTree(
            trees: List<CategoryTree>,
            categoryId: String,
            adTypeKey: AdTypeKey?
        ): CategoryTree? {
            trees.forEach { tree ->
                val isAdTypesPresent = if (adTypeKey != null) {
                    tree.adTypes.any { it.key == adTypeKey }
                } else true
                if (tree.category.id == categoryId && isAdTypesPresent) {
                    return tree
                } else {
                    val foundTree = findTree(
                        trees = tree.children,
                        categoryId = categoryId,
                        adTypeKey = adTypeKey
                    )
                    if (foundTree != null) return foundTree
                }
            }
            return null
        }

        fun findParentTree(
            trees: List<CategoryTree>,
            categoryId: String,
            adTypeKey: AdTypeKey?
        ): CategoryTree? {
            if (trees.isEmpty()) return null
            trees.forEach { tree ->
                val foundInChildren = tree.children.find { child ->
                    child.category.id == categoryId
                            && (adTypeKey == null || child.adTypes.any { it.key == adTypeKey })
                }
                if (foundInChildren != null) {
                    return tree
                } else {
                    val foundInChildrenOfChildren = findParentTree(
                        trees = tree.children,
                        categoryId = categoryId,
                        adTypeKey = adTypeKey
                    )
                    if (foundInChildrenOfChildren != null) return foundInChildrenOfChildren
                }
            }
            return null
        }

        fun findChildren(
            trees: List<CategoryTree>,
            categoryName: String,
        ): List<CategoryTree> {
            trees.forEach { tree ->
                if (tree.category.name == categoryName) {
                    return tree.children
                } else {
                    val children = findChildren(
                        trees = tree.children,
                        categoryName = categoryName
                    )
                    if (children.isNotEmpty()) return children
                }
            }
            return emptyList()
        }

        fun hasCategoryWithId(tree: CategoryTree, categoryId: String): Boolean {
            if (tree.category.id == categoryId) return true

            tree.children.forEach {
                val found = hasCategoryWithId(it, categoryId)
                if (found) return true
            }
            return false
        }
    }
}

data class AdType(
    val key: AdTypeKey,
    val name: String,
    val trackingName: String
)

enum class AdTypeKey {
    ALL,
    SELL,
    BUY,
    SWAP,
    LET,
    RENT,
    CO_RENT,
    VAC_RENT;

    companion object {
        fun safeValueOf(value: String): AdTypeKey = try {
            AdTypeKey.valueOf(value.uppercase())
        } catch (_: Exception) {
            SELL
        }
    }
}
