package se.scmv.morocco.domain.repositories

import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.ShopCategory
import se.scmv.morocco.domain.models.ShopSubscription
import se.scmv.morocco.domain.models.SignInResult

interface AuthenticationRepository {

    suspend fun signIn(
        emailOrPhone: String,
        password: String
    ): Resource<SignInResult, NetworkAndBackendErrors>

    suspend fun signInWithGoogle(idToken: String): Resource<SignInResult, NetworkAndBackendErrors>

    suspend fun sendSmsVerificationForSignUp(phoneNumber: String): Resource<String, NetworkAndBackendErrors>

    suspend fun sendSmsVerificationForPasswordReset(
        phoneNumber: String
    ): Resource<String, NetworkAndBackendErrors>

    suspend fun validatePhone(
        phoneNumber: String ? = null,
        code: String
    ): Resource<Boolean, NetworkAndBackendErrors>

    suspend fun sendPasswordResetLink(
        email: String
    ): Resource<String, NetworkAndBackendErrors>

    suspend fun registerAccount(
        phoneNumber: String,
        otpCode: String?,
        fullName: String,
        password: String,
    ): Resource<String, NetworkAndBackendErrors>

    suspend fun registerAccount(
        fullName: String,
        phoneNumber: String,
        email: String,
        shopCategory: ShopCategory,
        shopSubscription: ShopSubscription
    ): Resource<Unit, NetworkAndBackendErrors>

    suspend fun resetPassword(
        code: String,
        newPassword: String
    ): Resource<String, NetworkAndBackendErrors>

    /**
     * Don't use this directly in your ViewModel, use UpdatePasswordAndSignInUseCase instead
     * Since the backend forces us to re-login after changing the password to get a new token
     * Because they logout all devices after updating the password.
     */
    suspend fun updatePassword(
        currentPassword: String,
        newPassword: String
    ): Resource<Unit, NetworkAndBackendErrors>

    suspend fun signOut(): Resource<Boolean, NetworkAndBackendErrors>

    suspend fun checkAccountExistance(phone: String): Resource<Boolean, NetworkAndBackendErrors>
}