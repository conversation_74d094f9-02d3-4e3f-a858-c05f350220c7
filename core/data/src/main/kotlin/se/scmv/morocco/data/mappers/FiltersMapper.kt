package se.scmv.morocco.data.mappers

import com.apollographql.apollo3.api.Optional
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.repository.utils.Constants
import se.scmv.morocco.data.repository.utils.buildOrionComponentIconUrl
import se.scmv.morocco.data.rest.config.dtos.ListingCategoryFiltersDto
import se.scmv.morocco.domain.models.filter.ListMatch
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters.ChildParam
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters.Field
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters.FilterItems
import se.scmv.morocco.domain.models.filter.RangeParam
import se.scmv.morocco.domain.models.filter.SingleMatch
import se.scmv.morocco.domain.models.orion.OrionBaseComponent
import se.scmv.morocco.domain.models.orion.OrionBaseComponentData
import se.scmv.morocco.domain.models.orion.OrionComponentDependency
import se.scmv.morocco.domain.models.orion.OrionKeyStringItem
import se.scmv.morocco.domain.models.orion.OrionMinMax
import se.scmv.morocco.domain.models.orion.OrionMultipleSelectSmartDropDown
import se.scmv.morocco.domain.models.orion.OrionSelectExtended
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdown
import se.scmv.morocco.domain.models.orion.OrionSlider
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownChildData
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownItem
import se.scmv.morocco.domain.models.orion.OrionTextField
import se.scmv.morocco.domain.models.orion.OrionToggle
import se.scmv.morocco.domain.models.orion.Sort
import se.scmv.morocco.type.AdParamListTextFilter
import se.scmv.morocco.type.AdParamSingleBooleanFilter
import se.scmv.morocco.type.AdParamSingleNumericFilter
import se.scmv.morocco.type.AdParamSingleTextFilter
import se.scmv.morocco.type.AdParamsListMatchFilters
import se.scmv.morocco.type.AdParamsRangeFilter
import se.scmv.morocco.type.AdParamsSingleMatchFilters
import se.scmv.morocco.type.RangeFilter

fun ListingCategoryFiltersDto.Filters.toFilters(): ListingCategoryFilters.Filters {
    return ListingCategoryFilters.Filters(
        primaryFilters = primaryFilters.map { filter ->
            filter.let {
                ListingCategoryFilters.BaseFilters(
                    icon = filter.icon?.toBuildIconUrl(),
                    id = filter.id,
                    isParam = filter.isParam,
                    filterItems = filter.filterItems?.map { fItem ->
                        fItem.let {
                            FilterItems(
                                key = it.key,
                                name = it.name,
                                label = it.label,
                                short = it.short,
                                trackingName = it.trackingName,
                                children = it.children?.mapNotNull { child ->
                                    val key = child.key
                                    val label = child.label
                                    if (key != null && label != null) {
                                        ListingCategoryFilters.Child(
                                            key = child.key,
                                            label = child.label
                                        )
                                    } else return@mapNotNull null
                                }
                            )
                        }
                    } ?: emptyList(),
                    range = filter.range ?: emptyList(),
                    step = filter.step ?: 0,
                    suffix = filter.suffix ?: "",
                    name = filter.name,
                    type = filter.type,
                    child = filter.child ?: "",
                    fields = filter.fields?.map { field ->
                        Field(
                            id = field.id,
                            isParam = field.isParam,
                            name = field.name
                        )
                    } ?: emptyList(),
                    childParam = ChildParam(
                        id = filter.childParam?.id ?: "",
                        name = filter.childParam?.name ?: ""
                    ),
                    description = filter.description,
                    defaultValue = filter.defaultValue
                )
            }
        } ?: emptyList(),
        navBarFilters = navBarFilters.map { filter ->
            filter.let {
                ListingCategoryFilters.BaseFilters(
                    icon = filter.icon?.toBuildIconUrl(),
                    id = filter.id,
                    isParam = filter.isParam,
                    filterItems = filter.filterItems?.map { fItem ->
                        fItem.let {
                            FilterItems(
                                key = it.key,
                                name = it.name,
                                label = it.label,
                                short = it.short,
                                trackingName = it.trackingName,
                                children = it.children?.mapNotNull { child ->
                                    val key = child.key
                                    val label = child.label
                                    if (key != null && label != null) {
                                        ListingCategoryFilters.Child(
                                            key = child.key,
                                            label = child.label
                                        )
                                    } else return@mapNotNull null
                                }
                            )
                        }
                    } ?: emptyList(),
                    range = filter.range ?: emptyList(),
                    step = filter.step ?: 0,
                    suffix = filter.suffix ?: "",
                    name = filter.name,
                    type = filter.type,
                    child = filter.child ?: "",
                    fields = filter.fields?.map { field ->
                        Field(
                            id = field.id,
                            isParam = field.isParam,
                            name = field.name
                        )
                    } ?: emptyList(),
                    childParam = ChildParam(
                        id = filter.childParam?.id ?: "",
                        name = filter.childParam?.name ?: ""
                    ),
                    description = filter.description,
                    defaultValue = filter.defaultValue
                )
            }
        } ?: emptyList(),
        secondaryFilters = secondaryFilters.map { filter ->
            filter.let {
                ListingCategoryFilters.BaseFilters(
                    icon = filter.icon?.toBuildIconUrl(),
                    id = filter.id,
                    isParam = filter.isParam,
                    filterItems = filter.filterItems?.map { fItem ->
                        fItem.let {
                            FilterItems(
                                key = it.key,
                                name = it.name,
                                label = it.label,
                                short = it.short,
                                trackingName = it.trackingName,
                                children = it.children?.mapNotNull { child ->
                                    val key = child.key
                                    val label = child.label
                                    if (key != null && label != null) {
                                        ListingCategoryFilters.Child(
                                            key = child.key,
                                            label = child.label
                                        )
                                    } else return@mapNotNull null
                                }
                            )
                        }
                    } ?: emptyList(),
                    range = filter.range ?: emptyList(),
                    step = filter.step ?: 0,
                    suffix = filter.suffix ?: "",
                    name = filter.name,
                    type = filter.type,
                    child = filter.child ?: "",
                    fields = filter.fields?.map { field ->
                        Field(
                            id = field.id,
                            isParam = field.isParam,
                            name = field.name
                        )
                    } ?: emptyList(),
                    childParam = ChildParam(
                        id = filter.childParam?.id ?: "",
                        name = filter.childParam?.name ?: ""
                    ),
                    description = filter.description,
                    defaultValue = filter.defaultValue
                )
            }
        } ?: emptyList()
    )
}

// Extension function to convert ListMatch to AdParamsListMatchFilters
fun ListMatch?.toAdParamsListMatchFilters(): AdParamsListMatchFilters {
    return this?.let {
        AdParamsListMatchFilters(
            textList = Optional.presentIfNotNull(
                it.textList?.map { textParam ->
                    textParam.value?.let { values ->
                        AdParamListTextFilter(
                            name = textParam.name,
                            value = values
                        )
                    }
                }?.filterNotNull()
            )
        )
    } ?: AdParamsListMatchFilters() // Return an empty instance if ListMatch is null
}

// Extension function to convert SingleMatch to AdParamsSingleMatchFilters
fun SingleMatch?.toAdParamsSingleMatchFilters(): AdParamsSingleMatchFilters {
    return this?.let {
        AdParamsSingleMatchFilters(
            text = Optional.presentIfNotNull(
                it.text?.map { textParam ->
                    AdParamSingleTextFilter(
                        name = textParam.name,
                        value = textParam.value
                    )
                }
            ),
            numeric = Optional.presentIfNotNull(
                it.numeric?.map { numericParam ->
                    AdParamSingleNumericFilter(
                        name = numericParam.name,
                        value = numericParam.value
                    )
                }
            ),
            boolean = Optional.presentIfNotNull(
                it.boolean?.map { booleanParam ->
                    AdParamSingleBooleanFilter(
                        name = booleanParam.name,
                        value = booleanParam.value
                    )
                }
            )
        )
    } ?: AdParamsSingleMatchFilters() // Return an empty instance if SingleMatch is null
}

// Extension function to convert a Pair<Double, Double>? to RangeFilter
fun Pair<Double, Double>?.toRangeFilter(): RangeFilter? {
    return this?.let { (greaterValue, lesserValue) ->
        RangeFilter(
            greaterThanOrEqual = Optional.presentIfNotNull(greaterValue),
            lessThanOrEqual = Optional.presentIfNotNull(lesserValue)
        )
    } // Return an empty RangeFilter instance if Pair is null
}

fun List<RangeParam>?.toAdParamsRangeFilterList(): List<AdParamsRangeFilter> {
    return this?.map { rangeParam ->
        AdParamsRangeFilter(
            name = rangeParam.name,
            value = RangeFilter(
                greaterThanOrEqual = Optional.presentIfNotNull(rangeParam.value.greaterThanOrEqual),
                lessThanOrEqual = Optional.presentIfNotNull(rangeParam.value.lessThanOrEqual)
            )
        )
    } ?: emptyList()
}

// TODO Remove the above mappers and rename this function to toFilters once we use the new filters in shop page
internal fun ListingCategoryFiltersDto.Filters.toOrionFilters(): List<OrionBaseComponent> {
    val navBarFilters = navBarFilters.toOrionBaseComponents()
    val primaryFilters = primaryFilters.toOrionBaseComponents()
    val secondaryFilters = secondaryFilters.toOrionBaseComponents()

    return listOf(navBarFilters, primaryFilters, secondaryFilters).flatten()
}

internal fun List<ListingCategoryFiltersDto.BaseFilters>.toOrionBaseComponents(): List<OrionBaseComponent> {
    return filterNot { it.type == OrionComponentType.TEXT_FIELD.key && it.id == "keyword" }
        .map { it.toOrionBaseComponent() }.flatten()
}

internal fun ListingCategoryFiltersDto.BaseFilters.toOrionBaseComponent(): List<OrionBaseComponent> {
    return when (type) {
        OrionComponentType.SINGLE_SELECT_CATEGORY_DROPDOWN_SEARCH.key -> toOrionSingleSelectCategoryDropdown()
        OrionComponentType.TEXT_FIELD.key -> toOrionTextField(inputType = OrionTextField.InputType.TEXT)
        OrionComponentType.MEASURE_TEXT_FIELD.key -> toOrionTextField(
            inputType = OrionTextField.InputType.NUMBER,
        )

        OrionComponentType.TOGGLE_FIELD.key,
        OrionComponentType.TOGGLE_FIELD_FILTER.key -> toOrionToggle()

        OrionComponentType.MULTIPLE_SELECT_SMART_DROPDOWN.key,
        OrionComponentType.MULTIPLE_SELECT_SMART_DROPDOWN_ICON.key -> toOrionSmartDropdown()

        OrionComponentType.SINGLE_SELECT_EXTENDED.key -> toOrionSingleSelectExtended()
        OrionComponentType.MULTISELECT_EXTENDED.key -> toOrionMultipleSelectExtended()

        OrionComponentType.SLIDER.key -> toOrionSlider()
        OrionComponentType.MIN_MAX_FIELD.key -> toOrionMinMax()

        else -> null
    }?.let {
        // A hacky way to add the price sorting component after the price filter.
        // Since the config API doesn't support it yet.
        if (id == Constants.KEY_PRICE) {
            listOf(it, createPriceSortingComponent())
        } else {
            listOf(it)
        }
    } ?: emptyList()
}

internal fun ListingCategoryFiltersDto.BaseFilters.toOrionBaseComponentData() =
    OrionBaseComponentData(
        id = id,
        title = name.orEmpty(),
        required = false,
        iconUrl = icon?.let { buildOrionComponentIconUrl(it) }.orEmpty(),
        validations = emptyList(),
        dependencies = dependencies?.map {
            OrionComponentDependency(
                dependsOn = it.dependsOn,
                condition = when (it.condition.type) {
                    ListingCategoryFiltersDto.ConditionType.NOT_EMPTY -> OrionComponentDependency.Type.NOT_EMPTY
                    ListingCategoryFiltersDto.ConditionType.EQUALS -> OrionComponentDependency.Type.EQUALS
                }
            )
        }.orEmpty()
    )

internal fun ListingCategoryFiltersDto.BaseFilters.toOrionSingleSelectCategoryDropdown() =
    OrionSingleSelectCategoryDropdown(
        baseData = toOrionBaseComponentData(),
        allowParentSelection = true
    )

internal fun ListingCategoryFiltersDto.BaseFilters.toOrionTextField(
    inputType: OrionTextField.InputType,
    isLarge: Boolean = false,
) = OrionTextField(
    baseData = toOrionBaseComponentData(),
    enabled = true,
    suffix = suffix,
    isLarge = isLarge,
    inputType = inputType,
    potentialValue = null,
    notifyLapTitle = false,
    notifyLapDescription = false,
)

internal fun ListingCategoryFiltersDto.BaseFilters.toOrionToggle() = OrionToggle(
    baseData = toOrionBaseComponentData(),
    defaultValue = defaultValue,
)

@JvmName("toOrionKeyStringItemsOrEmptyFromFilterItems")
internal fun List<ListingCategoryFiltersDto.FilterItems>?.toOrionKeyStringItemsOrEmpty() =
    this?.mapNotNull {
        val key = it.key
        val name = it.label
        if (key != null && name != null) {
            OrionKeyStringItem(id = key, name = name)
        } else null
    }.orEmpty()

@JvmName("toOrionKeyStringItemsOrEmptyFromFields")
internal fun List<ListingCategoryFiltersDto.field>?.toOrionKeyStringItemsOrEmpty() = this?.map {
    OrionKeyStringItem(id = it.id, name = it.name)
}.orEmpty()

internal fun ListingCategoryFiltersDto.BaseFilters.toOrionSingleSelectExtended() =
    OrionSelectExtended(
        baseData = toOrionBaseComponentData(),
        allowMultiSelect = false,
        items = filterItems.toOrionKeyStringItemsOrEmpty()
    )

internal fun ListingCategoryFiltersDto.BaseFilters.toOrionMultipleSelectExtended() =
    OrionSelectExtended(
        baseData = toOrionBaseComponentData(),
        allowMultiSelect = true,
        items = fields.toOrionKeyStringItemsOrEmpty()
    )

internal fun ListingCategoryFiltersDto.BaseFilters.toOrionSlider() = OrionSlider(
    baseData = toOrionBaseComponentData(),
    items = filterItems.toOrionKeyStringItemsOrEmpty(),
    // TODO Workaround witing for this to be handled in the config API.
    orderOfSort = if (id == "regdate") Sort.DESC else Sort.ASC
)

internal fun ListingCategoryFiltersDto.BaseFilters.toOrionMinMax(): OrionMinMax {
    val rangeStart = range?.firstOrNull() ?: 0
    val rangeEnd = range?.lastOrNull() ?: 0
    return OrionMinMax(
        baseData = toOrionBaseComponentData(),
        suffix = suffix,
        range = rangeStart to rangeEnd
    )
}

internal fun ListingCategoryFiltersDto.BaseFilters.toOrionSmartDropdown(): OrionMultipleSelectSmartDropDown {
    return OrionMultipleSelectSmartDropDown(
        baseData = toOrionBaseComponentData(),
        childBaseData = OrionSmartDropDownChildData(
            id = childParam?.id.orEmpty(),
            title = childParam?.name.orEmpty(),
            required = false
        ),
        items = filterItems?.mapNotNull {
            val key = it.key
            val name = it.label
            val item = if (key != null && name != null) OrionSmartDropDownItem(
                id = key,
                name = name,
                trackingName = name.lowercase().replace(" ", ""),
                children = emptyList()
            ) else return@mapNotNull null
            val children = it.children?.mapNotNull { child ->
                val childKey = child.key
                val childName = child.label
                if (childKey != null && childName != null) OrionSmartDropDownItem(
                    id = childKey,
                    name = childName,
                    trackingName = childName.lowercase().replace(" ", ""),
                    children = emptyList()
                ) else return@mapNotNull null
            }.orEmpty()
            item.copy(children = children)
        }.orEmpty(),
    )
}

// TODO This should be discussed with the FE team to add in the new config API.
enum class PriceSort {
    None,
    ASC,
    DESC;

    companion object {
        fun getTitle(): String {
            return if (LocaleManager.isAr()) "ترتيب حسب السعر" else "Trier par prix"
        }
    }

    fun getLabel(): String {
        return when (this) {
            None -> if (LocaleManager.isAr()) "لا " else "Non"
            ASC -> if (LocaleManager.isAr()) "من الأدنى إلى الأعلى" else "Du moins chers"
            DESC -> if (LocaleManager.isAr()) "من الأعلى إلى الأدنى" else "Du plus chers"
        }
    }
}

internal fun createPriceSortingComponent() = OrionSelectExtended(
    baseData = OrionBaseComponentData(
        id = Constants.KEY_PRICE_SORTING,
        title = PriceSort.getTitle(),
        required = false,
        iconUrl = "",
        validations = listOf()
    ),
    allowMultiSelect = false,
    items = PriceSort.entries.map {
        OrionKeyStringItem(id = it.name, name = it.getLabel())
    }
)