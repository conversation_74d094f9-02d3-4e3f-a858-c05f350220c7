package se.scmv.morocco.data.database.daos

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import se.scmv.morocco.data.database.entities.TownEntity

@Dao
interface TownDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(towns: List<TownEntity>)

    @Query("SELECT * FROM towns")
    suspend fun getAll(): List<TownEntity>

    @Query("SELECT * FROM towns WHERE id = :id")
    suspend fun getById(id: String): TownEntity?

    @Query("SELECT * FROM towns WHERE id IN (:ids)")
    suspend fun getByIds(ids: List<String>): List<TownEntity>
}