package se.scmv.morocco.data.database.daos

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import se.scmv.morocco.data.database.entities.CityEntity
import se.scmv.morocco.data.database.entities.CityWithTownsEntity

@Dao
interface CityDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(cities: List<CityEntity>)

    @Query("SELECT * FROM cities")
    suspend fun getAll(): List<CityEntity>

    @Query("SELECT * FROM cities WHERE id = :id")
    suspend fun getById(id: String): CityEntity?

    @Query("SELECT * FROM cities WHERE id IN (:ids)")
    suspend fun getByIds(ids: List<String>): List<CityEntity>

    @Transaction
    @Query("SELECT * FROM cities WHERE id = :cityId")
    suspend fun getCityWithTowns(cityId: String): CityWithTownsEntity?

    @Query("DELETE FROM cities")
    suspend fun clear()
}


