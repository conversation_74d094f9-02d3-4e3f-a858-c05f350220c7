package se.scmv.morocco.data.repository.ad

import com.apollographql.apollo3.api.Optional
import se.scmv.morocco.GetAdForEditQuery
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsAddons
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.mappers.toGraphQlAdTypeKey
import se.scmv.morocco.data.repository.utils.Constants
import se.scmv.morocco.data.repository.utils.buildAdImageUrl
import se.scmv.morocco.data.repository.utils.buildAdVideoUrl
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringItem
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionMediaUploaderValue
import se.scmv.morocco.domain.models.orion.OrionSelectedKeysValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.type.AdTypeKey
import se.scmv.morocco.type.SubmitAdBooleanParamInput
import se.scmv.morocco.type.SubmitAdInput
import se.scmv.morocco.type.SubmitAdListKeyParamInput
import se.scmv.morocco.type.SubmitAdLocationInput
import se.scmv.morocco.type.SubmitAdMediaIDInput
import se.scmv.morocco.type.SubmitAdMediaInput
import se.scmv.morocco.type.SubmitAdNumericParamInput
import se.scmv.morocco.type.SubmitAdParamsInput
import se.scmv.morocco.domain.models.AdTypeKey as DomainAdTypeKey

object AdInsertHelper {
    private val explicitKeys = listOf(
        Constants.KEY_CATEGORY,
        Constants.KEY_PRICE,
        Constants.KEY_TITLE,
        Constants.KEY_DESCRIPTION,
        Constants.KEY_NAME,
        Constants.KEY_PHONE,
        Constants.KEY_PHONE_HIDDEN,
        Constants.KEY_IMAGE_UPLOAD,
        Constants.KEY_VIDEO_UPLOAD,
        Constants.KEY_CITY,
        Constants.KEY_AREA,
        Constants.KEY_ADDRESS,
    )

    fun buildSubmitAdInput(
        adID: String?,
        values: List<OrionBaseComponentValue>,
        account: Account.Connected
    ): SubmitAdInput {
        val explicitValues = values.filter { it.id in explicitKeys }
        val implicitValues = values.filterNot { it.id in explicitKeys }

        val categoryValue =
            explicitValues.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                .map { it.category.id to it.adTypeKey }
                .firstOrNull()
        val keyStringValues = explicitValues.filterIsInstance<OrionKeyStringValue>()

        return SubmitAdInput(
            adId = Optional.presentIfNotNull(adID),
            category = categoryValue?.first.orEmpty(),
            type = categoryValue?.second?.toGraphQlAdTypeKey() ?: AdTypeKey.SELL,
            price = keyStringValues.firstOrNull { it.id == Constants.KEY_PRICE }?.value?.toIntOrNull() ?: 0,
            title = keyStringValues.firstOrNull { it.id == Constants.KEY_TITLE }?.value.orEmpty(),
            description = keyStringValues.firstOrNull { it.id == Constants.KEY_DESCRIPTION }?.value.orEmpty(),
            name = account.connectedContact().name,
            phone = keyStringValues.firstOrNull { it.id == Constants.KEY_PHONE }?.value.orEmpty(),
            phoneHidden = explicitValues.filterIsInstance<OrionKeyBooleanValue>()
                .firstOrNull { it.id == Constants.KEY_PHONE_HIDDEN }?.value ?: false,
            location = buildSubmitAdLocationInput(keyStringValues),
            isSifm = Optional.present(false),
            media = buildSubmitAdMediaInput(explicitValues),
            params = buildSubmitAdParamsInput(implicitValues)
        )
    }

    private fun buildSubmitAdLocationInput(
        keyStringValues: List<OrionKeyStringValue>
    ): SubmitAdLocationInput {
        return SubmitAdLocationInput(
            city = keyStringValues.firstOrNull { it.id == Constants.KEY_CITY }?.value.orEmpty(),
            area = keyStringValues.firstOrNull { it.id == Constants.KEY_AREA }?.value.let {
                Optional.present(it)
            },
            address = keyStringValues.firstOrNull { it.id == Constants.KEY_ADDRESS }?.value.let {
                Optional.present(it)
            },
        )
    }

    private fun buildSubmitAdMediaInput(
        explicitValues: List<OrionBaseComponentValue>
    ): Optional<SubmitAdMediaInput> {
        val mediaValues = explicitValues.filterIsInstance<OrionMediaUploaderValue>()
        return Optional.present(
            SubmitAdMediaInput(
                images = mediaValues
                    .filter { it.id == Constants.KEY_IMAGE_UPLOAD}
                    .map { value -> value.medias.map { SubmitAdMediaIDInput(id = it.id) } }
                    .flatten(),
                videos = mediaValues
                    .filter { it.id == Constants.KEY_VIDEO_UPLOAD}
                    .map { value -> value.medias.map { SubmitAdMediaIDInput(id = it.id) } }
                    .flatten(),
                images360 = listOf()
            )
        )
    }

    private fun buildSubmitAdParamsInput(
        implicitValues: List<OrionBaseComponentValue>
    ): SubmitAdParamsInput {
        val numeric = mutableListOf<SubmitAdNumericParamInput>()
        val boolean = mutableListOf<SubmitAdBooleanParamInput>()
        val list = mutableListOf<SubmitAdListKeyParamInput>()

        implicitValues.filter { it.stringValue().isNotBlank() }.forEach { value ->
            when (value) {
                is OrionKeyStringValue -> {
                    list.add(SubmitAdListKeyParamInput(id = value.id, value = value.value))
                }

                is OrionKeyBooleanValue -> {
                    boolean.add(SubmitAdBooleanParamInput(id = value.id, value = value.value))
                }

                is OrionSelectedKeysValue -> value.keys.forEach { key ->
                    boolean.add(SubmitAdBooleanParamInput(id = key, value = true))
                }
            }
        }

        return SubmitAdParamsInput(numeric = numeric, boolean = boolean, listKey = list)
    }

    fun buildAdEditValues(ad: GetAdForEditQuery.GetAdForEdit): List<OrionBaseComponentValue> {
        val components = mutableListOf<OrionBaseComponentValue>()
        components.add(
            OrionSingleSelectCategoryDropdownValue(
                id = Constants.KEY_CATEGORY,
                category = Category(
                    id = ad.category,
                    name = "",
                    icon = "",
                    trackingName = "",
                ),
                adTypeKey = DomainAdTypeKey.safeValueOf(ad.type.name)
            )
        )
        components.add(OrionKeyStringValue(id = Constants.KEY_TITLE, value = ad.title))
        components.add(OrionKeyStringValue(id = Constants.KEY_DESCRIPTION, value = ad.description))
        components.add(OrionKeyStringValue(id = Constants.KEY_PRICE, value = ad.price.toString()))
        components.add(OrionKeyStringValue(id = Constants.KEY_NAME, value = ad.name))
        components.add(OrionKeyStringValue(id = Constants.KEY_PHONE, value = ad.phone))
        components.add(OrionKeyBooleanValue(id = Constants.KEY_PHONE_HIDDEN, value = ad.phoneHidden))
        components.add(OrionKeyStringValue(id = Constants.KEY_CITY, value = ad.location.city))
        ad.location.area?.let { area ->
            components.add(OrionKeyStringValue(id = Constants.KEY_AREA, value = area))
        }
        ad.location.address?.let { address ->
            components.add(OrionKeyStringValue(id = Constants.KEY_ADDRESS, value = address))
        }
        ad.media?.let { media ->
            components.add(
                OrionMediaUploaderValue(
                    id = Constants.KEY_IMAGE_UPLOAD,
                    medias = media.images.map {
                        OrionKeyStringItem(id = it.id, name = buildAdImageUrl(it.defaultPath))
                    },
                )
            )
            components.add(
                OrionMediaUploaderValue(
                    id = Constants.KEY_VIDEO_UPLOAD,
                    medias = media.videos.map {
                        OrionKeyStringItem(id = it.id, name = buildAdVideoUrl(it.defaultPath))
                    },
                )
            )
        }
        ad.params.forEach {
            it.onAdEditListKeyParam?.let { param ->
                components.add(OrionKeyStringValue(id = param.id, value = param.keyValue))
            }
            it.onAdEditBooleanParam?.let { param ->
                components.add(OrionKeyBooleanValue(id = param.id, value = param.booleanValue))
            }
        }
        return components
    }

    fun trackInsertAdAndVasSelection(
        analyticsHelper: AnalyticsHelper,
        account: Account.Connected,
        category: Category?,
        adTypeKey: DomainAdTypeKey?,
        city: City?,
        isLimit: Boolean,
        errorCode: String? = null,
        vasPack: VasPack?,
        vasPackage: VasPackage?
    ) {
        val properties = mutableSetOf(
            Param(key = AnalyticsEvent.ParamKeys.LANG, value = LocaleManager.getCurrentLanguage())
        ).apply {
            category?.getAnalyticsParams()?.let { addAll(it) }
            add(
                Param(
                    key = AnalyticsEvent.ParamKeys.AD_TYPE,
                    value = adTypeKey?.name?.lowercase().orEmpty()
                )
            )
            city?.getAnalyticsParams()?.let { addAll(it) }
            setOf(
                Param(
                    key = AnalyticsEvent.ParamKeys.SELLER_ID,
                    value = account.connectedContact().accountId
                ),
                Param(
                    key = AnalyticsEvent.ParamKeys.SELLER_TYPE,
                    value = account.analyticsAccountType()
                ),
                Param(
                    key = AnalyticsEvent.ParamKeys.SELLER_PHONE,
                    value = account.connectedContact().phone.orEmpty()
                ),
                Param(
                    key = AnalyticsEvent.ParamKeys.VALUE,
                    value = if (isLimit) {
                        AnalyticsEvent.ParamValues.LIMIT
                    } else AnalyticsEvent.ParamValues.NO_LIMIT
                )
            ).let { addAll(it) }
            if (errorCode != null) {
                add(Param(AnalyticsEvent.ParamKeys.ERROR_TYPE, errorCode))
            }
        }
        analyticsHelper.logEvent(
            event = AnalyticsEvent(name = AnalyticsEvent.Types.INSERT_AD, properties = properties),
            where = setOf(AnalyticsAddons.FIREBASE)
        )
        if (vasPack != null && vasPackage != null) {
            properties.add(Param(AnalyticsEvent.ParamKeys.VAS_ID, vasPackage.id))
            properties.add(Param(AnalyticsEvent.ParamKeys.VAS_NAME, vasPack.title))
            properties.add(
                Param(AnalyticsEvent.ParamKeys.VAS_DURATION, vasPackage.durationDays.toString())
            )
            analyticsHelper.logEvent(
                event = AnalyticsEvent(
                    name = AnalyticsEvent.Types.VAS_SELECTION,
                    properties = properties
                ),
                where = setOf(AnalyticsAddons.FIREBASE)
            )
        }
    }

    fun trackAdInserted(
        analyticsHelper: AnalyticsHelper,
        eventName: String,
        account: Account.Connected,
        category: Category?,
        adTypeKey: DomainAdTypeKey?,
        city: City?,
        isLimit: Boolean,
        errorCode: String? = null,
    ) {
        val properties = mutableSetOf(
            Param(key = AnalyticsEvent.ParamKeys.LANG, value = LocaleManager.getCurrentLanguage())
        ).apply {
            category?.getAnalyticsParams()?.let { addAll(it) }
            add(
                Param(
                    key = AnalyticsEvent.ParamKeys.AD_TYPE,
                    value = adTypeKey?.name?.lowercase().orEmpty()
                )
            )
            city?.getAnalyticsParams()?.let { addAll(it) }
            setOf(
                Param(
                    key = AnalyticsEvent.ParamKeys.SELLER_ID,
                    value = account.connectedContact().accountId
                ),
                Param(
                    key = AnalyticsEvent.ParamKeys.SELLER_TYPE,
                    value = account.analyticsAccountType()
                ),
                Param(
                    key = AnalyticsEvent.ParamKeys.SELLER_PHONE,
                    value = account.connectedContact().phone.orEmpty()
                ),
                Param(
                    key = AnalyticsEvent.ParamKeys.VALUE,
                    value = if (isLimit) {
                        AnalyticsEvent.ParamValues.LIMIT
                    } else AnalyticsEvent.ParamValues.NO_LIMIT
                )
            ).let { addAll(it) }
            if (errorCode != null) {
                add(Param(AnalyticsEvent.ParamKeys.ERROR_TYPE, errorCode))
            }
        }
        analyticsHelper.logEvent(
            event = AnalyticsEvent(name = eventName, properties = properties),
            where = setOf(AnalyticsAddons.FIREBASE)
        )

    }
}